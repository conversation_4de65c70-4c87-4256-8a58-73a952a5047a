import { forwardRef, useImperativeHandle, useState } from "react";
import ReactMarkdown from 'react-markdown';
import { Link } from "react-router-dom";
import './SuccessAlert.min.css';

type NextStep = {
	nextStepLinkText: string,
	nextStepLinkUrl: string,
}

interface SuccessAlertProps {
	style?: React.CSSProperties;
}

export default forwardRef(function SuccessAlert(props: SuccessAlertProps, ref) {
	const [message, setMessage] = useState('');
	const [nextStep, setNextStep] = useState<NextStep>();

	useImperativeHandle(ref, () => {
		return {
			show,
			close
		}
	}, []);

	// optionally accepts a link
	function show(successMessage: string, nextStep: NextStep) {
		setMessage(successMessage);
		nextStep && setNextStep(nextStep);
	}

	function close() {
		setMessage('');
	}

	if (message) {
		return (
			<div className="notification is-success position-bottom" style={props.style} >
				<button className="delete" onClick={close}></button>
				<ReactMarkdown>{message}</ReactMarkdown>
				{" "}
				{nextStep?.nextStepLinkUrl && <Link to={nextStep.nextStepLinkUrl}>{nextStep.nextStepLinkText}</Link>}
			</div>
		)
	} else {
		return null;
	}

})
