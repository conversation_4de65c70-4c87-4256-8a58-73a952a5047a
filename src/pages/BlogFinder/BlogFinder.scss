@import "../../assets/themes/mainTheme";
@import "../../assets/fonts/customFonts";

@import "bulma/sass/utilities/all";
@import "bulma/sass/base/all";
@import "bulma/sass/helpers/typography";
@import "bulma/sass/grid/columns";
@import "bulma/sass/elements/container";
@import 'bulma/sass/form/_all';
@import "bulma/sass/components/tabs";
@import "bulma/sass/helpers/all";
@import "bulma/sass/elements/all";

@import "../../assets/bulma-overrides/bulmaOverrides";

@media (max-width: 450px) {
    .blog-finder-responsive-content {
        display: none;
    }

    .blog-finder-upload-container {
        padding: 10px;
    }

    .blog-finder-upload-box {
        max-width: 240px;

        .upload-input {
            width: 188px;
        }

    }
}

    .blog-form-container .abun-table-responsive .abun-table-filter-buttons {
        @media (min-width:980px) {
            width: 600px !important;
        }

        .btns-container {
            @media (max-width: 768px) {
                margin-top: 0rem !important;
            }
        }
    }

.blog-container{
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    // padding: 20px 0 0 0;
    font-family: $primary-font !important;
    overflow: visible;
    width: 100%;

    h1{
        font-family: $primary-font !important;
        font-size: 2rem !important;
        font-weight: 600 !important;
    }

    p {
        color: rgba(0,0,0,.698);
        font-family: $secondary-font !important;
        font-size: 1.125rem!important;
    }

    .blog-form-container {
        align-self: stretch;
        // margin-left: 20px;
        // margin-right: 50px;
        padding-top: 20px;

        .blog-form {
            width: 95%;
            margin: auto !important;
        }

        .blog-form-group {
            margin-bottom: 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 15px;
            align-items: flex-start !important;
            max-width: 1200px;
            width: 100%;
            margin: auto;

            .blog-finder-input-container {
                flex-grow: 1;
                margin: 0 auto;
                position: relative;
                width: 100%;
                display: flex;
                justify-content: center;
                align-items: center;

            }

            button{
                width: auto;
                border: none;
                font-family: $secondary-font !important;
                background: #2bc482;
                color: #ffffff;
                padding: 10px;
                font-size: 16px;
                border-radius: 4px;
                cursor: pointer;

                &:disabled{
                    border: none;
                cursor: not-allowed;
                background: #96E6C4;
                color: white;
                border: 1px solid #d3d3d3;
                }
            }
        }

        .abun-table-responsive {

            tbody {
                color: #000;
            }
        }

    }
}