import { faSync } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormGroup from '@mui/material/FormGroup';
import { styled } from '@mui/material/styles';
import Switch, { SwitchProps } from '@mui/material/Switch';
import { useMutation, useQuery } from "@tanstack/react-query";
import { ColumnDef, createColumnHelper } from "@tanstack/react-table";
import { useEffect, useRef, useState } from "react";
import { NavLink, useLoaderData, useNavigate, useRouteLoaderData } from "react-router-dom";
import { Tooltip } from "react-tooltip";
import AbunButton from "../../components/AbunButton/AbunButton";
import AbunModal from "../../components/AbunModal/AbunModal";
import AbunTable from "../../components/AbunTable/AbunTable";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import Icon from "../../components/Icon/Icon";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import {
    addSitemap,
    addWebsitePageURL,
    checkJobStatus,
    findWebsiteSitemaps,
    getScrapedWebPages,
    getWebsiteAnalysisStatsQueryFn,
    removeWebpageUrlMutation,
    reScrapeWebsitePages,
    saveIncludeLinking,
    saveSummaryMutation,
    scrapeMoreWebsitePages,
} from "../../utils/api";
import { BasePageData } from "../Base/Base";
import { ConnectWebsite } from "../ConnectWebsite/LazyConnectWebsiteModal";
import '../KeywordsResearchV2/ResearchedKeywords.min.css';
import { pageURL } from "../routes";
import './WebsiteScanning.min.css';

interface WebsiteScanData {
    url: string;
    title: string;
    summary: string;
    lastScanned: string;
    include_linking: string
}

interface CustomizedSwitchProps {
    url: string;
    include_linking: string;
    handleSaveLinking: (url: string, switch_link: string) => void;
}


interface ServerData {
    website_connected: boolean
    sitemap_urls: Array<string>
    domain: string
    is_crawling: boolean
    finding_sitemaps: boolean
    has_more_pages: boolean
}

interface WebsiteAanalysisStats {
    total_pages: number,
    pages_scanned: number,
    progress: number,
    estimated_time_left: number,
    steps: {
        crawling: string,
        analyzing: string,
        generating: string
    },
    is_analysis_complete: boolean,
    time_display: string
}

export default function WebsiteScanning() {
    // ---------------------- NON STATE CONSTANTS ----------------------
    const pageSizes = [15, 25, 50, 100];
    const isProduction = process.env.REACT_APP_DRF_DOMAIN === "https://api.abun.com";

    // ----------------------------- LOADER -----------------------------
    const pageData: ServerData = useLoaderData() as ServerData;
    const basePageData: BasePageData = useRouteLoaderData("base") as BasePageData;

    // ----------------------- REFS -----------------------
    const successAlertRef = useRef<any>(null);
    const failAlertRef = useRef<any>(null);

    // -------------------------- NAVIGATOR --------------------------
    const navigate = useNavigate();

    // -------------------------- STATES --------------------------
    const [tableData, setTableData] = useState<Array<WebsiteScanData>>([]);
    const [isCrawling, setIsCrawling] = useState(pageData.is_crawling);
    const [sitemapUrl, setSitemapUrl] = useState(pageData.sitemap_urls[0] || "");
    const [websitePageUrl, setWebsitePageUrl] = useState("");
    const [showAddWebsitePageUrlModal, setShowAddWebsitePageUrlModal] = useState(false);
    const [showConnectWebsiteModal, setShowConnectWebsiteModal] = useState(!pageData.website_connected);
    const [showSitemapUrlModal, setShowSitemapUrlModal] = useState(pageData.sitemap_urls.length === 0 && !pageData.finding_sitemaps && basePageData.user_verified);
    const [isLoading, setIsLoading] = useState(pageData.finding_sitemaps);
    const [loadingDataText, setLoadingDataText] = useState(pageData.finding_sitemaps ? "Finding Sitemaps..." : "Loading Data...");
    const [websiteAnalysisStats, setWebsiteAnalysisStats] = useState<WebsiteAanalysisStats>();
    const [showSchemaModal, setShowSchemaModal] = useState(false)
    const [isScrapeMore, setIsScrapeMore] = useState(false)
    const [rescanLink, setRescanLink] = useState("")
    const [editedSummary, setEditedSummary] = useState("")
    const [disabledButtons, setDisabledButtons] = useState<{ [key: string]: boolean }>({});
    const textareaRef = useRef<HTMLTextAreaElement | null>(null);
    const tableRef = useRef<{ refetchData: () => Promise<void> }>(null);
    const [jobId, setJobId] = useState("");
    const [scrapeMoreRunning, setScrapeMoreRunning] = useState(localStorage.getItem(`${pageData.domain}scrapeMoreRunning`) === "true");
    const [analysisStepText, setAnalysisStepText] = useState("")
    const [linkingState, setLinkingState] = useState<Map<string, string>>(new Map());

    // -------------------------- QUERIES --------------------------
    const { isFetching, isError, data, refetch } = useQuery(getScrapedWebPages());
    const {
        data: websiteAnalysisStatsData,
    } = useQuery({
        queryKey: ['websiteAnalysisStats'],
        queryFn: () => getWebsiteAnalysisStatsQueryFn(),
        refetchInterval: 5000,
        refetchIntervalInBackground: false,
        enabled: isCrawling,
    });

    const rescanQuery = checkJobStatus(jobId);

    const { data: jobStatus } = useQuery({
        ...rescanQuery,
        refetchInterval: 5000,
        enabled: !!jobId,
        onSuccess: (result: any) => {
            if (result.status === "completed") {
                setDisabledButtons(prev => ({ ...prev, [rescanLink]: false }));
                refetch();
                setJobId("");
            }
        },
        onError: (error) => {
            setDisabledButtons(prev => ({ ...prev, [rescanLink]: false }));
            setJobId("");
        },
    });

    // -------------------------- MUTATIONS --------------------------
    const addSiteMapMutation = useMutation(addSitemap);
    const addWebsitePageURLMutation = useMutation(addWebsitePageURL);
    const reScrapeWebsitePagesMutation = useMutation(reScrapeWebsitePages);
    const scrapeMoreWebsitePagesMutation = useMutation(scrapeMoreWebsitePages);
    const removeWebpageUrl = useMutation(removeWebpageUrlMutation);
    const saveSummary = useMutation(saveSummaryMutation);
    const saveIncludeLinkingMut = useMutation(saveIncludeLinking);

    // ----------------------- EFFECTS -----------------------
    useEffect(() => {
        document.title = "Internal Links | Abun";
    }, []);

    useEffect(() => {
        if (showSitemapUrlModal) {
            setIsLoading(true);
            setLoadingDataText("Finding Sitemaps...");

            // Try to find the sitemap before showing the modal
            findWebsiteSitemaps().then((response) => {
                const responseData: any = response.data;
                if (responseData.success) {
                    setShowSitemapUrlModal(false);
                    setIsCrawling(true);
                }
            }).catch(error => {
                console.log(error);
            }).finally(() => {
                setIsLoading(false);
            })
        }
    }, []);

    useEffect(() => {
        if (showSchemaModal) {
            setTimeout(adjustTextareaHeight, 0);
        }
    }, [showSchemaModal, editedSummary]);


    useEffect(() => {
        if (data) {
            const newTableData = (data as any)['data']['web_pages'];
            setTableData(newTableData);
            const initialMap = new Map<string, string>();
            newTableData.forEach((row: WebsiteScanData) => {
                initialMap.set(row.url, row.include_linking);
            });
            setLinkingState(initialMap);

        }
    }, [data]);

    useEffect(() => {
        if (websiteAnalysisStatsData && (websiteAnalysisStatsData as any)['data']) {
            setWebsiteAnalysisStats((websiteAnalysisStatsData as any)['data']['stats']);
        }
    }, [websiteAnalysisStatsData]);

    useEffect(() => {
        if (websiteAnalysisStats && websiteAnalysisStats.is_analysis_complete) {
            // Refresh the page when analysis is complete
            updateScrapeMoreRunning(false)
            window.location.reload();
        }
    }, [websiteAnalysisStats]);


    useEffect(() => {
        const steps = websiteAnalysisStats?.steps;

        if (!steps) return;
        if (steps.crawling === 'in_progress') {
            setAnalysisStepText("Crawling website pages");
        } else if (steps.analyzing === 'in_progress') {
            setAnalysisStepText("Analyzing content structure");
        } else {
            setAnalysisStepText("Generating optimization suggestions");
        }
    }, [websiteAnalysisStats?.steps]);


    function CustomizedSwitch({ url, include_linking, handleSaveLinking }: CustomizedSwitchProps) {
        const IOSSwitch = styled((props: SwitchProps) => (
            <Switch focusVisibleClassName=".Mui-focusVisible" disableRipple {...props} />
        ))(({ theme }) => ({
            width: 42,
            height: 26,
            padding: 0,
            '& .MuiSwitch-switchBase': {
                padding: 0,
                margin: 2,
                transitionDuration: '300ms',
                '&.Mui-checked': {
                    transform: 'translateX(16px)',
                    color: '#fff',
                    '& + .MuiSwitch-track': {
                        backgroundColor: '#65C466',
                        opacity: 1,
                        border: 0,
                        ...theme.applyStyles('dark', {
                            backgroundColor: '#2ECA45',
                        }),
                    },
                    '&.Mui-disabled + .MuiSwitch-track': {
                        opacity: 0.5,
                    },
                },
                '&.Mui-focusVisible .MuiSwitch-thumb': {
                    color: '#33cf4d',
                    border: '6px solid #fff',
                },
                '&.Mui-disabled .MuiSwitch-thumb': {
                    color: theme.palette.grey[100],
                    ...theme.applyStyles('dark', {
                        color: theme.palette.grey[600],
                    }),
                },
                '&.Mui-disabled + .MuiSwitch-track': {
                    opacity: 0.7,
                    ...theme.applyStyles('dark', {
                        opacity: 0.3,
                    }),
                },
            },
            '& .MuiSwitch-thumb': {
                boxSizing: 'border-box',
                width: 22,
                height: 22,
            },
            '& .MuiSwitch-track': {
                borderRadius: 26 / 2,
                backgroundColor: '#E9E9EA',
                opacity: 1,
                transition: theme.transitions.create(['background-color'], {
                    duration: 500,
                }),
                ...theme.applyStyles('dark', {
                    backgroundColor: '#39393D',
                }),
            },
        }));

        return (
            <FormGroup sx={{ display: "flex", alignItems: "center", justifyContent: "center" }}>
                <FormControlLabel
                    control={<IOSSwitch sx={{ m: 1 }} defaultChecked={include_linking === "on"}
                        onChange={(e) => handleSaveLinking(url, e.target.checked ? "on" : "off")} />}
                    label=""
                />
            </FormGroup>
        );
    }

    // ---------------------- TABLE COLUMN DEFS ----------------------
    const columnHelper = createColumnHelper<WebsiteScanData>();
    const columnDefs: ColumnDef<any, any>[] = [
        columnHelper.accessor((row: WebsiteScanData) => row.url, {
            id: 'url',
            header: "URL",
            cell: props => <NavLink to={props.row.original.url} target="_blank" style={{
                display: 'block',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
            }}>{props.row.original.url}</NavLink>,
            enableGlobalFilter: true,
            enableSorting: false,
            meta: { align: 'left' }
        }),
        columnHelper.accessor((row: WebsiteScanData) => row.summary, {
            id: "summary",
            header: "Summary",
            cell: (props) => {
                const summaryValue = props.getValue();

                return (
                    <div className="website-scanning-summary-cell">
                        <div className="summary-content"
                            data-tooltip-id="article-setting-tooltip"
                            data-tooltip-content={summaryValue} >{summaryValue}
                            <Tooltip id="article-setting-tooltip" place="bottom" positionStrategy="fixed" className="summary-content-tooltip" />
                        </div>
                        <button
                            onClick={() => handleEditeSummary(summaryValue, props.row.original.url)}
                            className="website-scanning-edit-button"
                        >
                            ✏️
                        </button>
                    </div>
                );
            },
            enableGlobalFilter: true,
            meta: { align: "left" },
        }),
        columnHelper.display({
            id: 'Linking',
            header: "Include in Linking",
            cell: (props) => {
                const rowUrl = props.row.original.url;
                const switchValue = linkingState.get(rowUrl) ?? "off";
                return (
                    <CustomizedSwitch
                        url={rowUrl}
                        include_linking={switchValue}
                        handleSaveLinking={handleSaveLinking}
                    />
                );
            },
            meta: { align: 'center' }
        }),
        columnHelper.display({
            id: 'rescan',
            header: "Rescan Page",
            cell: ({ row }) => (
                <button
                    style={{
                        width: "100px",
                        cursor: disabledButtons[row.original.url] ? "not-allowed" : "pointer"
                    }}
                    className="button is-success is-outlined is-small more-rounded-borders"
                    onClick={() => handleRescan(row.original.url)}
                    disabled={disabledButtons[row.original.url]}
                >
                    {disabledButtons[row.original.url] ? "" : <FontAwesomeIcon icon={faSync} style={{ marginRight: '7px' }} />}
                    {disabledButtons[row.original.url] ? "Rescanning..." : "Rescan"}
                </button>
            ),
            meta: { align: 'center' }
        }),
        columnHelper.display({
            id: 'lastScanned',
            header: "Last Scanned",
            cell: props => (
                <span style={{ color: '#000',}}>
                    {props.row.original.lastScanned}
                </span>
            ),
            enableGlobalFilter: true,
            meta: { align: 'center' }
        }),
        columnHelper.display({
            id: 'delete',
            header: "Action",
            cell: (cellProps) => (
                <div style={{ textAlign: 'center' }}>
                    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"
                        onClick={() => handleDelete(cellProps.row.original.url)}>
                        <g clip-path="url(#clip0_48_5565)">
                            <g clip-path="url(#clip1_48_5565)">
                                <path d="M3.15356 6.32313C3.44461 10.8562 3.72319 13.2144 3.88856 14.3369C3.97256 14.9046 4.34531 15.3672 4.90346 15.5011C5.66306 15.6839 6.9713 15.8906 9.00075 15.8906C11.0302 15.8906 12.3381 15.6839 13.098 15.5014C13.6559 15.3676 14.0286 14.9049 14.1126 14.3373C14.2783 13.2144 14.5566 10.8562 14.8476 6.32214" stroke="black" stroke-width="1.05" stroke-linecap="round" stroke-linejoin="round" />
                                <path d="M11.3087 3.47962C12.4769 3.50128 13.3871 3.53672 14.0394 3.56986C14.8236 3.60923 15.552 4.02694 15.7712 4.78097C15.804 4.89417 15.8349 5.01394 15.8618 5.14092C15.9911 5.74467 15.5392 6.26344 14.924 6.31561C13.9331 6.39928 12.1195 6.49444 8.99249 6.49444C5.86579 6.49444 4.05191 6.39928 3.0613 6.31561C2.44574 6.26377 1.99129 5.74139 2.15043 5.14486C2.20785 4.92994 2.2784 4.73372 2.35255 4.55948C2.61932 3.93506 3.26146 3.61284 3.93937 3.57544C4.56543 3.54131 5.47663 3.50259 6.69135 3.47962C6.87108 3.07198 7.16548 2.7254 7.53869 2.48211C7.9119 2.23882 8.34781 2.10932 8.79332 2.10938H9.20741C9.65286 2.10938 10.0887 2.23891 10.4618 2.4822C10.835 2.72549 11.129 3.07203 11.3087 3.47962Z" stroke="black" stroke-width="1.05" stroke-linecap="round" stroke-linejoin="round" />
                                <path d="M7.03125 9.32812L7.35937 12.6094" stroke="black" stroke-width="1.05" stroke-linecap="round" stroke-linejoin="round" />
                                <path d="M10.9687 9.32812L10.6406 12.6094" stroke="black" stroke-width="1.05" stroke-linecap="round" stroke-linejoin="round" />
                            </g>
                        </g>
                        <defs>
                            <clipPath id="clip0_48_5565">
                                <rect width="16.8" height="16.8" fill="white" transform="translate(0.600098 0.599976)" />
                            </clipPath>
                            <clipPath id="clip1_48_5565">
                                <rect width="16.8" height="16.8" fill="white" transform="translate(0.600098 0.599976)" />
                            </clipPath>
                        </defs>
                    </svg>
                </div>
            ),
            meta: { align: 'center' }

        }),
    ];

    // ---------------------- HANDLERS ----------------------
    const handleAddWebsitePageUrl = () => {
        if (!websitePageUrl.trim()) return;

        let url: URL | null;

        try {
            url = new URL(websitePageUrl);
        } catch {
            failAlertRef.current?.show("Please enter a valid URL.");
            return;
        }

        if (url.hostname !== pageData.domain) {
            failAlertRef.current?.show("The internal link must match the website's domain.");
            return;
        }

        const linkExists = tableData.some(link => link.url === websitePageUrl);
        if (linkExists) {
            failAlertRef.current?.show("This internal link already exists.");
            return;
        }

        addWebsitePageURLMutation.mutate({ pageUrl: websitePageUrl }, {
            onSuccess: () => {
                setShowAddWebsitePageUrlModal(false);
                setWebsitePageUrl(""); // Clear input
                successAlertRef.current?.show("Internal link added successfully!");
                refetch(); // Refresh the table data
                setTimeout(() => {
                    successAlertRef.current?.close();
                }, 5000);
            },
            onError: () => {
                failAlertRef.current?.show("Failed to add internal link. Please try again after some time.");
                setTimeout(() => {
                    failAlertRef.current?.close();
                }, 5000);
            }
        });
    };

    const handleEditeSummary = (summary: string, url: string) => {
        setEditedSummary(summary);
        setRescanLink(url)
        setShowSchemaModal(true)
    }

    const adjustTextareaHeight = () => {
        if (textareaRef.current) {
            textareaRef.current.style.height = "auto"; // Reset height
            textareaRef.current.style.height = textareaRef.current.scrollHeight + "px"; // Adjust height
        }
    };

    function handleDelete(url: string) {
        removeWebpageUrl.mutate({
            url: url
        }, {
            onSuccess: () => {
                refetch();
                successAlertRef.current?.show("Internal link removed successfully!");
                setTimeout(() => {
                    successAlertRef.current?.close();
                }, 5000);
            },
            onError: () => {
                failAlertRef.current?.show("Failed to remove Internal link. Please try again after some time.");
                setTimeout(() => {
                    failAlertRef.current?.close();
                }, 5000);
            }
        })

    }

    const handleRescan = (pageUrl: string) => {
        setRescanLink(pageUrl)
        setDisabledButtons(prev => ({ ...prev, [pageUrl]: true }));

        addWebsitePageURLMutation.mutate(
            { pageUrl, rescan: true },
            {
                onSuccess: (data) => {
                    const jobId = data['data']['job_id']
                    if (!jobId) {
                        setDisabledButtons(prev => ({ ...prev, [pageUrl]: false }));
                        return;
                    }
                    setJobId(jobId);
                    successAlertRef.current?.show("Your rescan request is being processed. Please wait.");

                    setTimeout(() => {
                        successAlertRef.current?.close();
                    }, 5000);
                },
                onError: () => {
                    failAlertRef.current?.show("Failed to rescan internal link. Please try again after some time.");
                    setDisabledButtons(prev => ({ ...prev, [pageUrl]: false }));
                    setTimeout(() => {
                        failAlertRef.current?.close();
                    }, 5000);
                },
            }
        );
    };

    const handleBackClick = () => {
        setIsCrawling(false)
        setIsScrapeMore(false)
        refetch()
    };

    const updateScrapeMoreRunning = (value) => {
        localStorage.setItem(`${pageData.domain}scrapeMoreRunning`, value.toString());
        setScrapeMoreRunning(value);
    };

    const handleSaveLinking = (url: string, switch_link: string) => {
        const previousValue = linkingState.get(url) ?? "off";        
        setLinkingState((prev) => {
            const newMap = new Map(prev);
            newMap.set(url, switch_link);
            return newMap;
        });
        saveIncludeLinkingMut.mutate({
            url: url,
            switch_link: switch_link
        }, {
            onSuccess: () => {
                setTableData((prev) =>
                    prev.map((row) =>
                        row.url === url ? { ...row, include_linking: switch_link } : row
                    )
                );
                successAlertRef.current?.show("Switch Linking Saved!");
                setTimeout(() => {
                    successAlertRef.current?.close();
                }, 5000);
            },
            onError: () => {
                setLinkingState((prev) => {
                    const newMap = new Map(prev);
                    newMap.set(url, previousValue);
                    return newMap;
                });

                failAlertRef.current?.show("Failed to save Linking. Please try again after some time.");                
                setTimeout(() => {
                    failAlertRef.current?.close();
                }, 5000);
            }
        })
    }

    const handleSaveSummary = (summary: string, url: string) => {
        saveSummary.mutate({
            summary: summary,
            url: url
        }, {
            onSuccess: () => {
                setShowSchemaModal(false)
                refetch()
                successAlertRef.current?.show("Summary Saved!");
                setTimeout(() => {
                    successAlertRef.current?.close();
                }, 5000);
            },
            onError: () => {
                setShowSchemaModal(false)
                failAlertRef.current?.show("Failed to save Summary. Please try again after some time.");
                setTimeout(() => {
                    failAlertRef.current?.close();
                }, 5000);
            }
        })
    }

    const handleAddSitemapUrl = () => {
        let url: URL | null;
        try {
            url = new URL(sitemapUrl);
        } catch {
            url = null;
        }

        if (!url) {
            failAlertRef.current?.show("Please enter a valid URL.");
        } else if (!sitemapUrl.trim()) {
            failAlertRef.current?.show("Please enter the sitemap URL.");
        } else if (!isValidSitemapUrl(url)) {
            failAlertRef.current?.show("The provided URL is not a valid sitemap URL. It should end with .xml or .txt");
        } else if (url.hostname !== pageData.domain) {
            failAlertRef.current?.show("The sitemap URL must match the website's domain.");
        } else {
            addSiteMapMutation.mutate(sitemapUrl, {
                onSuccess: (response: any) => {
                    const responseData: any = response.data;

                    if (responseData.success) {
                        setShowSitemapUrlModal(false);
                        setIsCrawling(true);
                        successAlertRef.current?.show("Sitemap URL added successfully!");
                        setTimeout(() => {
                            successAlertRef.current?.close();
                        }, 5000);
                    } else {
                        failAlertRef.current?.show(responseData.message);
                        setTimeout(() => {
                            failAlertRef.current?.close();
                        }, 5000);
                    }

                },
                onError: (response) => {
                    console.log(response)
                    failAlertRef.current?.show("Failed to add sitemap url. Please try again after some time.");
                    setTimeout(() => {
                        failAlertRef.current?.close();
                    }, 5000);
                }
            });
        }
    };

    // ---------------------- FUNCTIONS ----------------------
    function isValidSitemapUrl(parsedUrl: URL): boolean {
        try {
            return parsedUrl.pathname.endsWith('.xml') || parsedUrl.pathname.endsWith('.txt');
        } catch (error) {
            return false; // Return false if URL is not valid
        }
    }

    const getStepClass = (stepStatus) => {
        if (stepStatus === 'completed') return 'completed';
        if (stepStatus === 'in_progress') return 'active';
        return '';
    };

    // ============================================================
    // --------------------- MAIN RENDER CODE ---------------------
    // ============================================================
    if (isCrawling || isScrapeMore) {
        return (
            <div className="analysis-progress-container">
                {(isScrapeMore || scrapeMoreRunning) &&
                    <div className='w-100 is-flex is-justify-content-flex-start'>
                        <svg className={"back-btn"} onClick={handleBackClick} stroke="#bcbcbc" fill="#bcbcbc" width="28" height="24" viewBox="0 0 28 24">
                            <path d="M27.5 12H2M2 12L13 1M2 12L13 23" stroke="black" strokeOpacity="0.5" strokeWidth="2" />
                        </svg>
                    </div>
                }
                <div className="analysis-card">
                    <h1 className="analysis-title">Website Analysis in Progress</h1>
                    <p className="analysis-subtitle">Our AI is analyzing your website & finding all the internal pages based on your sitemap.</p>

                    {
                        websiteAnalysisStats?.total_pages ?
                            <>
                                <h4 className={"is-size-5 has-text-centered"}>
                                    {analysisStepText}&nbsp;&nbsp;<Icon iconName="spinner" />
                                </h4>

                                <div className="progress-bar">
                                    <div className="progress-fill" style={{ width: `${websiteAnalysisStats.progress}%` }}></div>
                                </div>

                                <div className="analysis-stats">
                                    <div className="stat-item">
                                        <h2>{websiteAnalysisStats.progress}%</h2>
                                        <p>Progress</p>
                                    </div>
                                    <div className="stat-item">
                                        <h2>{websiteAnalysisStats.time_display}</h2>
                                        <p>Minutes Left</p>
                                    </div>
                                    <div className="stat-item">
                                        <h2>{websiteAnalysisStats.pages_scanned || 0}</h2>
                                        <p>Pages Scanned</p>
                                    </div>
                                </div>
                            </>
                            : (isScrapeMore && !scrapeMoreRunning) ? (<div style={{ textAlign: "center", marginTop: "1rem" }}>
                                <AbunButton
                                    className={"is-primary is-small"}
                                    disabled={scrapeMoreWebsitePagesMutation.isLoading}
                                    type={"primary"} clickHandler={() => {
                                        scrapeMoreWebsitePagesMutation.mutate(undefined, {
                                            onSuccess: () => {
                                                setIsCrawling(true);
                                                setIsScrapeMore(false)
                                                updateScrapeMoreRunning(true);
                                                setLoadingDataText("Finding Pages...")
                                            },
                                            onError: () => {
                                                failAlertRef.current?.show("Failed to send scrape more pages request. Please try again after some time.");
                                                setTimeout(() => {
                                                    failAlertRef.current?.close();
                                                }, 5000);
                                            }
                                        })
                                    }}>
                                    Start Scrape More?
                                </AbunButton>
                            </div>) :
                                <>
                                    <h4 className={"is-size-5 has-text-centered"}>
                                        {loadingDataText}&nbsp;&nbsp;<Icon iconName="spinner" />
                                    </h4>
                                </>
                    }
                </div>

                <div className="analysis-steps-card">
                    <h3>Analysis Steps</h3>
                    <ul>
                        <li className={getStepClass(websiteAnalysisStats?.steps.crawling)}>
                            Crawling website pages
                        </li>
                        <li className={getStepClass(websiteAnalysisStats?.steps.analyzing)}>
                            Analyzing content structure
                        </li>
                        <li className={getStepClass(websiteAnalysisStats?.steps.generating)}>
                            Generating optimization suggestions
                        </li>
                    </ul>
                </div>

                <div className="next-steps-card">
                    <h3>Next:</h3>
                    <p>Once we have scanned all of your websites internal pages, we will automatically link them in the articles based on how relevant they are.</p>
                    <br />
                    <p>We will also be showing up all the links. You can manually add more pages or rescan.</p>
                    <br />
                    <p>The amount of pages scanned is dependent on the plan you are on.</p>
                </div>
            </div>
        );
    } else if (isFetching || isLoading) {
        return (
            <p style={{ textAlign: "center", fontSize: "1.3rem" }} className="mt-5">
                {loadingDataText}
            </p>
        );
    } else if (isError) {
        return (
            <section className="section">
                <div className="container">
                    <div className="box">
                        <h1 className="title has-text-centered">Internal Links</h1>
                        <p className="has-text-centered is-size-5">
                            Failed to load data. Please try again later.
                        </p>
                    </div>
                </div>
            </section>
        );
    } else if (!basePageData.user_verified) {
        return (
            <section className="section">
                <div className="container">
                    <div className="box">
                        <h1 className="title has-text-centered">Internal Links</h1>
                        <p className="has-text-centered is-size-5">
                            You will need to verify your email to use this feature.
                        </p>
                    </div>
                </div>
            </section>
        );
    }

    return (
        <>
            <div className="tab-content w-100 website-scanning-container">
                <div className="">
                    <div className="">
                        <div className={"is-flex is-justify-content-center is-align-items-center is-flex-direction-column has-text-centered"}>
                            <h1 className={"is-size-4"}>Internal Links Manager</h1>
                            <p className={"is-size-6 mt-2 mb-2"}>Manage your Website Links which will be used for Internal Linking within Articles.</p>
                        </div>

                        <div className="is-flex is-flex-direction-row  is-justify-content-space-between">
                            <div className='is-flex'>
                                <AbunButton
                                    className={"is-primary is-small mr-4"}
                                    style={{ background: '#2E64FE' }}
                                    disabled={addWebsitePageURLMutation.isLoading}
                                    type={"primary"} clickHandler={() => {
                                        setShowAddWebsitePageUrlModal(true);
                                    }}>
                                    <svg width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg" style={{ marginRight: '0.2rem', verticalAlign: 'middle', height: 'auto', width: '16px', stroke: '#fff' }}>
                                        <path d="M15.75 9.5C15.75 9.64918 15.6907 9.79226 15.5852 9.89775C15.4798 10.0032 15.3367 10.0625 15.1875 10.0625H9.5625V15.6875C9.5625 15.8367 9.50324 15.9798 9.39775 16.0852C9.29226 16.1907 9.14918 16.25 9 16.25C8.85082 16.25 8.70774 16.1907 8.60225 16.0852C8.49676 15.9798 8.4375 15.8367 8.4375 15.6875V10.0625H2.8125C2.66332 10.0625 2.52024 10.0032 2.41475 9.89775C2.30926 9.79226 2.25 9.64918 2.25 9.5C2.25 9.35082 2.30926 9.20774 2.41475 9.10225C2.52024 8.99676 2.66332 8.9375 2.8125 8.9375H8.4375V3.3125C8.4375 3.16332 8.49676 3.02024 8.60225 2.91475C8.70774 2.80926 8.85082 2.75 9 2.75C9.14918 2.75 9.29226 2.80926 9.39775 2.91475C9.50324 3.02024 9.5625 3.16332 9.5625 3.3125V8.9375H15.1875C15.3367 8.9375 15.4798 8.99676 15.5852 9.10225C15.6907 9.20774 15.75 9.35082 15.75 9.5Z" fill="white"></path>
                                    </svg>
                                    Add Internal Link
                                </AbunButton>

                                {
                                    (!pageData.is_crawling || scrapeMoreRunning) && !isProduction && basePageData.user_verified &&
                                    <AbunButton
                                        className={"is-primary is-small mr-4"}
                                        style={{ background: '#2E64FE' }}
                                        disabled={reScrapeWebsitePagesMutation.isLoading}
                                        type={"primary"} clickHandler={() => {
                                            reScrapeWebsitePagesMutation.mutate(undefined, {
                                                onSuccess: () => {
                                                    setIsCrawling(true);
                                                    setLoadingDataText("Finding Sitemaps...")
                                                },
                                                onError: () => {
                                                    failAlertRef.current?.show("Failed to send re-scrape website pages request. Please try again after some time.");
                                                    setTimeout(() => {
                                                        failAlertRef.current?.close();
                                                    }, 5000);
                                                }
                                            })
                                        }}>
                                        <FontAwesomeIcon icon={faSync} style={{ marginRight: '7px' }} />
                                        Re-Scrape Full Website Again
                                    </AbunButton>
                                }
                                {
                                    (!pageData.is_crawling || scrapeMoreRunning) && pageData.has_more_pages && basePageData.currentPlanName !== 'Trial' &&
                                    <AbunButton
                                        className={"is-primary is-small"}
                                        style={{ background: '#2E64FE' }}
                                        disabled={scrapeMoreWebsitePagesMutation.isLoading}
                                        type={"primary"} clickHandler={() => {
                                            setIsScrapeMore(true)
                                            if (scrapeMoreRunning) {
                                                setIsCrawling(true)
                                            }
                                        }}>
                                        Find more Internal pages ?
                                    </AbunButton>
                                }
                            </div>
                        </div>

                        <AbunTable
                            ref={tableRef}
                            serverSide={true}
                            tableContentName={"Internal Links"}
                            apiUrl="/api/frontend/get-scraped-webpages/"
                            tableData={tableData}
                            columnDefs={columnDefs}
                            pageSizes={pageSizes}
                            enableSorting={true}
                            initialPageSize={pageSizes[1]}
                            transformResponse={(rawData) => ({
                                data: rawData.web_pages.map((page: any) => ({
                                    url: page.url,
                                    lastScanned: page.lastScanned,
                                    summary: page.summary,
                                    title: page.title,
                                    include_linking: page.include_linking
                                })),
                                total: rawData.total_count
                            })}
                            noDataText={"No internal links data available."}
                            searchboxPlaceholderText={"Search internal links..."}
                        />
                    </div>
                </div>
            </div>

            {/* Add Internal Link Modal */}
            <AbunModal
                active={showAddWebsitePageUrlModal}
                headerText={"Add Your Internal Link"}
                closeable={true}
                closeableKey={true}
                hideModal={() => setShowAddWebsitePageUrlModal(false)}
            >
                <div className="has-text-centered">
                    <div className="field">
                        <div className="control">
                            <input
                                type="url"
                                className="input"
                                style={{ borderRadius: '10px' }}
                                placeholder="Enter your internal link here..."
                                value={websitePageUrl}
                                onChange={(e) => setWebsitePageUrl(e.target.value)}
                                onKeyDown={(e) => {
                                    if (e.key === 'Enter') {
                                        handleAddWebsitePageUrl();
                                    }
                                }}
                            />
                        </div>
                    </div>

                    <AbunButton
                        type="success"
                        className="mt-4"
                        disabled={!websitePageUrl.trim() || addWebsitePageURLMutation.isLoading} // Disable if input is empty
                        clickHandler={handleAddWebsitePageUrl}
                    >
                        {addWebsitePageURLMutation.isLoading ? "Adding..." : "Add Internal Link"}
                    </AbunButton>
                </div>
            </AbunModal>

            {/* Add Sitemap Url Modal */}
            <AbunModal
                active={showSitemapUrlModal}
                headerText={"Add Your Website Sitemap"}
                closeable={true}
                hideModal={() => {
                    navigate(pageURL["createArticle"]);
                }}
            >
                <div className="has-text-centered">
                    <div className="field">
                        <div className="control">
                            <input
                                type="url"
                                className="input"
                                placeholder="Enter your website sitemap url here..."
                                value={sitemapUrl}
                                onChange={(e) => setSitemapUrl(e.target.value)}
                                onKeyDown={(e) => {
                                    if (e.key === 'Enter') {
                                        handleAddSitemapUrl();
                                    }
                                }}
                            />
                        </div>
                    </div>

                    <AbunButton
                        type="success"
                        className="mt-4"
                        disabled={!sitemapUrl.trim() || addSiteMapMutation.isLoading || !basePageData.user_verified} // Disable if input is empty
                        clickHandler={handleAddSitemapUrl}
                    >
                        {addSiteMapMutation.isLoading ? "Adding..." : "Add"}
                    </AbunButton>
                </div>
            </AbunModal>

            <AbunModal active={showSchemaModal}
                headerText=""
                closeable={true}
                closeableKey={true}
                modelWidth="800px"
                hideModal={() => {
                    setShowSchemaModal(false)
                }}>
                <div className={"has-text-centered"}>
                    <textarea
                        className="abun-script mt-2"
                        value={editedSummary}
                        onChange={(e) => {
                            setEditedSummary(e.target.value);
                            adjustTextareaHeight();
                        }}
                        ref={textareaRef}
                        style={{
                            width: "100%",
                            resize: "none",
                            border: "1px solid #ccc",
                            padding: "10px",
                            fontFamily: "$secondary-font",
                            overflow: "hidden", // Hide scrollbar
                            minHeight: "3rem", // Initial height
                        }}
                    />
                    <AbunButton type={"success"}
                        className={"mt-4"}
                        clickHandler={() => { handleSaveSummary(editedSummary, rescanLink) }}
                    >
                        Save
                    </AbunButton>
                </div>
            </AbunModal>


            <SuccessAlert ref={successAlertRef} />
            <ErrorAlert ref={failAlertRef} />

            {showConnectWebsiteModal && (
                <ConnectWebsite
                    setShowConnectWebsiteModal={setShowConnectWebsiteModal}
                    failAlertRef={failAlertRef}
                    successAlertRef={successAlertRef}
                    navigateOrReload="reload"
                />
            )}
        </>
    );
}