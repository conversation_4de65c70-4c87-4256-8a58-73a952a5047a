import { useQuery } from "@tanstack/react-query";
import { ColumnDef, createColumnHelper } from "@tanstack/react-table";
import { useEffect, useRef, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import newTab from '../../assets/images/new-tab.webp';
import AbunButton from "../../components/AbunButton/AbunButton";
import AbunLoader from "../../components/AbunLoader/AbunLoader";
import AbunTable from "../../components/AbunTable/AbunTable";
import CustomContextMenu from "../../components/CustomContextMenu/CustomContextMenu";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import { getRedditPostFinderQueriesView, getTaskProgress } from "../../utils/api";
import './RedditPostFinder.min.css';


interface RedditQueryResult {
    post_title: string,
    post_link: string,
    query: string,
    position: number,
}


export default function RedditPostFinderView() {
    // ---------------------- NON STATE CONSTANTS ----------------------
    const pageSizes = [15, 25, 50, 100];

    // -------------------------- STATES --------------------------
    const [queries, setQueries] = useState<Array<RedditQueryResult>>([]);
    const { queryID } = useParams() as { queryID: string };
    const [query, setQuery] = useState("");
    const [processing, setIsProcessing] = useState(false);
    const navigate = useNavigate();

    // -------------------------- REFS --------------------------
    const errorAlertRef = useRef<any>(null);
    const successAlertRef = useRef<any>(null);

    // -------------------------- QUERIES --------------------------
    const {
        isFetching,
        isError,
        data,
        refetch
    } = useQuery({
        queryKey: ['getRedditPostFinderQueriesView', queryID],
        queryFn: () => getRedditPostFinderQueriesView(queryID),
        refetchOnWindowFocus: false
    });

    // ---------------------- EFFECTS ----------------------
    useEffect(() => {
        const storedTaskId = localStorage.getItem("task_id");
        if (storedTaskId) {
            pollTaskProgress(storedTaskId);
        }
    }, []);

    const pollTaskProgress = (taskId) => {
        setIsProcessing(true);
        const interval = setInterval(() => {
            getTaskProgress(taskId)
                .then((res) => {
                    const status = res.data.status;
                    if (status === "success") {
                        console.log("progress succeed")
                        clearInterval(interval);
                        setIsProcessing(false);
                        setTimeout(() => successAlertRef.current?.close(), 5000);
                        localStorage.removeItem("task_id");
                        refetch();
                    } else if (status === "failure") {
                        clearInterval(interval);
                        setIsProcessing(false);
                        errorAlertRef.current?.show("Task failed. Please try again.");
                        setTimeout(() => errorAlertRef.current?.close(), 5000);
                    }
                })
                .catch((err) => {
                    console.error("Error fetching task progress:", err);
                    clearInterval(interval);
                    setIsProcessing(false);
                    errorAlertRef.current?.show("Error fetching task progress.");
                    setTimeout(() => errorAlertRef.current?.close(), 5000);
                });
        }, 2000);
    };

    useEffect(() => {
        if (data) {
            // Function to replace null/undefined with "N/A"
            const setDefaults = (item: RedditQueryResult): RedditQueryResult => ({
                post_title: item.post_title || "N/A",
                post_link: item.post_link || "N/A",
                query: item.query || "N/A",
                position: item.position ?? "N/A",
            });

            // Transform data
            const transformedQueries = data['data']['queries'].map(setDefaults);

            setQueries(transformedQueries);

            if (transformedQueries.length > 0) {
                setQuery(transformedQueries[0].query);
            }
        }
    }, [data]);

    // ---------------------- EFFECTS ----------------------
    useEffect(() => {
        document.title = "Reddit Post Finder | Abun"
    }, []);

    // ---------------------- TABLE COLUMN DEFS ----------------------
    const columnHelper = createColumnHelper<RedditQueryResult>();
    const columnDefs: ColumnDef<any, any>[] = [
        columnHelper.accessor((row: RedditQueryResult) => row.post_title, {
            id: 'URL',
            header: "Top Reddit Post URL",
            cell: (props) => {
            
                return (
                    <CustomContextMenu
                        url={props.row.original.post_link}
                        CtrlOrMetaClick={() => {
                            openUrlInNewTab(props.row.original.post_link);
                        }}
                        normalClick={() => {
                            window.open(props.row.original.post_link);
                        }}>
                        <span style={{ cursor: 'pointer' }}>
                            {props.row.original.post_title}
                        </span>
                    </CustomContextMenu>
                );
            },
            enableGlobalFilter: true,
        }),
        columnHelper.accessor((row: RedditQueryResult) => row.post_title, {
            id: 'new tab',
            header: "",
            cell: (props) => {
                return (
                <AbunButton
                    width={"70px"}
                    style={{ height: "30px", fontSize: "smaller"}}                 
                    type={"primary"}
                    clickHandler={() => {
                        window.open(props.row.original.post_link, '_blank');
                    }}>
                    Open
                    <img src={newTab} alt="new-tab" style={{ height: 'auto', width: '1.4em', filter: 'invert(1)', marginLeft: '0.5em' }} />
                </AbunButton>
                )
            },
            // enableGlobalFilter: true,
            meta: {
                align: 'center'
            }
        }),
        // columnHelper.accessor((row: RedditQueryResult) => row.position, {
        //     id: 'SERP',
        //     header: "SERP Rank",
        //     cell: info => info.getValue() || "N/A",
        //     enableGlobalFilter: true,
        //     meta: {
        //         align: 'center'
        //     }
        // }),
    ]
    function goBack() {
        navigate(-1);
    }
    const openUrlInNewTab = (url: string) => {
        window.open(url, "_blank");
    }
    function downloadCSV() {
        if (queries.length === 0) {
            errorAlertRef.current?.show("No data available to download.");
            return;
        }

        console.log("queries",queries)

        const escapeCSV = (value) => {
            if (value == null) return "";
            const stringValue = String(value);            
            return `"${stringValue.replace(/"/g, '""')}"`;
        };
        
        // Extract relevant columns (Reddit Post URL and SERP Rank)
        const csvData = [
            ["Reddit Post URL", "Reddit Post Title", "SERP Rank"],
            ...queries.map((row) => [
                escapeCSV(row.post_link),
                escapeCSV(row.post_title),
                escapeCSV(row.position)
            ])
        ];

        // Convert array to CSV format
        const csvContent = csvData.map(e => e.join(",")).join("\n");

        // Create a Blob and trigger download
        const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.setAttribute("href", url);
        link.setAttribute("download", `Abun.com - ${query}_REDDIT_SEO.csv`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Show success alert after the file is downloaded
        setTimeout(() => {
            successAlertRef.current?.show("CSV file has been downloaded successfully.");
        }, 500);
    }


    // ============================================================
    // --------------------- MAIN RENDER CODE ---------------------
    // ============================================================
    if (processing || isFetching) {
        return (
            <>
                <div className="w-100">
                      <div className={""} style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        minHeight: '95vh',
                        }}
                    >
                        <AbunLoader show={processing || isFetching} height="50vh" />
                    </div>
                </div>
            </>
        );
    }

    else if (isError) {
        return (
             <div className="card w-100" style={{ minHeight: '100vh' }}>
                <div className="container">
                    <div className={"card-content"}>
                        <h1 className="title has-text-centered">No Opportunities Found</h1>
                        <p className="has-text-centered not-found-p is-size-5 mb-4">
                            No Reddit SEO opportunities found for this topic. Try a different keyword or niche.
                        </p>
                        <p style={{justifySelf: "center"}}>
                        <button onClick={goBack} className="back-btn button is-success" style={{ cursor: "pointer" }}>                            
                        <span className="left-arrow mr-2">➜ </span>  Go Back
                        </button>
                        </p>
                    </div>
                </div>
            </div>
        );
    } else {
        return (
            <>
                <div className="reddit-post-view-table w-100">
                    <div className={""}>
                        <span className={"back-btn"} style={{ cursor: "pointer" }} onClick={goBack}>
                            <svg className="back-btn" width="30" height="24" viewBox="0 0 30 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M26.0435 12.0003H2.82031M2.82031 12.0003L12.8382 1.98242M2.82031 12.0003L12.8382 22.0181" stroke="black" stroke-opacity="0.5" stroke-width="3" />
                            </svg>
                        </span>
                        <div className={"w-100 is-flex is-align-items-center is-flex-direction-column mt-3"}>
                            <h1>
                                Topic / Niche : {query}
                            </h1>
                        </div>
                        <div className={"abun-table-button-container is-flex is-justify-content-end mt-6 mr-4"}>
                            <button
                                className={`button is-primary is-small is-justify-content-space-between is-flex is-align-items-center`}
                                onClick={downloadCSV}
                            >
                                <p className={"btn-txt"}>Download as CSV</p>
                            </button>
                        </div>

                        <div className="post-project-abun-table">
                        <AbunTable tableContentName={"Reddit Post Queries"}
                            tableData={queries}
                            columnDefs={columnDefs}
                            pageSizes={pageSizes}
                            initialPageSize={pageSizes[0]}
                            noDataText={"No Queries data available."}
                        />
                        </div>

                        <SuccessAlert ref={successAlertRef} />
                        <ErrorAlert ref={errorAlertRef} />
                    </div>
                </div>
            </>
        );
    }
}
