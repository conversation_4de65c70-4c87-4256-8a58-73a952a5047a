// GuestPostFinder.scss
@import "../../assets/themes/mainTheme";
@import "../../assets/fonts/customFonts";

@import "bulma/sass/utilities/all";
@import "bulma/sass/base/all";
@import "bulma/sass/helpers/typography";
@import "bulma/sass/grid/columns";
@import "bulma/sass/elements/container";
@import 'bulma/sass/form/_all';
@import "bulma/sass/components/tabs";
@import "bulma/sass/helpers/all";
@import "bulma/sass/elements/all";

@import "../../assets/bulma-overrides/bulmaOverrides";

.reddit-post-table {
    font-family: $primary-font;

    h1 {
        font-family: $primary-font !important;
        font-size: 2rem !important;
        font-weight: 600 !important;
    }
 }

.post-project-abun-table {
    .abun-table-responsive {

        tbody {
            color: #000;
        }
    }
}

.reddit-post-container{

    .ca-label {
        font-size: 18px;
        color: #333;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .ca-input::placeholder {
        @media(max-width:480px) {
            font-size: 13px;
        }
    }
    
    .ca-input {
        padding: 10px;
        font-size: 14px;
        //border: 1px solid #ccc;
        // border-radius: 4px;
        background-color: #fff;
        border: 1px solid #b0b0b0;
        border-radius: 10px;
        margin-bottom: 30px;
    }

}

.reddit-container{
    font-family: $primary-font !important;

    h1{
        font-family: $primary-font !important;
        font-size: 2rem !important;
        font-weight: 600 !important;
    }
    .reddit-p{
        color: rgba(0, 0, 0, .698);
        font-family: $secondary-font !important;
        font-size: 1.125rem !important;
    }
    .reddit-form{
        justify-items: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px;
    }
}

.left-arrow {
  display: inline-block;
  transform: scaleX(-1);
}

.reddit-post-view-table{
    h1{
        font-size: 2em;
        font-weight: normal;
        text-align: center;
        font-family: $primary-font !important;
    }
}