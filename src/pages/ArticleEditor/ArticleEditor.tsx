/* eslint-disable jsx-a11y/anchor-is-valid */
import { Player } from "@lottiefiles/react-lottie-player";
import { FormControl, InputLabel, MenuItem, Select } from '@mui/material';
import Box from '@mui/material/Box';
import Chip from '@mui/material/Chip';
import { useMutation, useQuery } from "@tanstack/react-query";
import { AiEditor, OpenaiModelConfig } from "aieditor";
import "aieditor/dist/style.css";
import dayjs, { Dayjs } from "dayjs";
import "easymde/dist/easymde.min.css";
import { Dispatch, MutableRefObject, SetStateAction, useEffect, useRef, useState } from "react";
import { NavLink, useNavigate, useParams, useRouteLoaderData, useLocation } from "react-router-dom";
import { Tooltip } from "react-tooltip";
import removeMarkdown from 'remove-markdown';
import 'tippy.js/animations/scale.css';
import 'tippy.js/dist/tippy.css';
import newTab from '../../assets/images/new-tab.webp';
import AbunButton from "../../components/AbunButton/AbunButton";
import AbunLoader from "../../components/AbunLoader/AbunLoader";
import AbunModal from "../../components/AbunModal/AbunModal";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import GenericButton from "../../components/GenericButton/GenericButton";
import Icon from "../../components/Icon/Icon";
import Input from "../../components/Input/Input";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import { pageURL } from "../../pages/routes";
import {
	addCategoryMutation,
	addGhlCategoryMutation,
	articleIsGeneratedQuery,
	connectWebsiteQuery,
	editTitleMutation,
	generateNewFeaturedImageMutation,
	loadArticleContentQuery,
	makeApiRequest,
	postArticleMutation,
	removeFeaturedImageMutation,
	saveArticleMutation,
	scheduleArticleMutation,
	searchImageMutation,
	twyaSubmitMutation,
	uploadFeaturedImageMutation,
	wpPostCategoryCheckedMutation,
	saveSettingsMutation,
} from "../../utils/api";
import { AllIntegrations } from "../Articles/ShowV2Articles";
import { BasePageData } from "../Base/Base";
import ConnectWebsiteLoading from "../ConnectWebsite/ConnectWebsiteLoading";
import WebsiteCompetitors from "../ConnectWebsite/WebsiteCompetitors";
import WebsiteDomain from "../ConnectWebsite/WebsiteDomain";
import WebsiteICP from "../ConnectWebsite/WebsiteICP";
import WebsiteTitle from "../ConnectWebsite/WebsiteTitle";
import './ArticleEditor.min.css';

export interface Categories {
	id: number
	name: string
}

interface SearchImage {
	url: string;
	alt: string;
}

interface ConnectWebsiteProps {
	setShowConnectWebsiteModal: React.Dispatch<React.SetStateAction<boolean>>
	successAlertRef: MutableRefObject<any>
	failAlertRef: MutableRefObject<any>
}
interface WebsiteData {
	domain: string
	protocol: string
	blog: string
	title: string
	description: string
	industry: string
	icp: string
	language: string
	competitors: Array<string>
	generate_content_plan: boolean
}
interface ArticleIsGenerated {
	generated: boolean
	failed: boolean
	title: string
	keyword: string
	traffic: number
	keyword_hash: string
	keyword_project_id: string
	keyword_project_name: string
	locationIsoCode: string
}


interface GetArticleContentResponse {
	is_posted: boolean
	posted_to: string
	article_post_status: "draft" | "publish"
	processing: boolean
	title: string
	content: string
	keyword: string
	traffic: number
	internal_links: number | null
	external_links: number | null
	image_count: number | null
	word_count: number | null
	featured_image: string
	all_integrations_with_unique_id: Array<AllIntegrations>
	feature_image_template: Array<FeatureImageTemplate>
	selected_template: string
	external_backlinks_preference: string
	image_source: string
	article_description: string
	article_scheduled_for_posting: boolean
	article_scheduled_datetime: string
	article_feedback: string
	post_link: string
	posted_on: string
	article_language_preference: string
	suggested_internal_links?: Array<SuggestedInternalLink>
	url_slug: string,
	active_integration: string,
}

type FeatureImageTemplate = {
	template_id: string,
	sample_image_url: string
}

interface UploadFeaturedImageProps {
	articleUid: string
	featuredImageURL: string
	setFeaturedImageURL: Dispatch<SetStateAction<string | null>>
	hideModal: () => void
	errorAlertRef: MutableRefObject<any>
	successAlertRef: MutableRefObject<any>
}

type Link = {
	linkHref: string
	linkTxt: string
	rel?: string
}

type SuggestedInternalLink = {
	phrase: string
	link: string
}


export default function ArticleEditor() {
	// ------------------- REACT ROUTER -------------------
	const { articleUID } = useParams() as { articleUID: string };
	const navigate = useNavigate();
	const basePageData = useRouteLoaderData("base") as BasePageData;

	const { active_website_domain } = basePageData;
	const [showConnectWebsiteWarningModal, setShowConnectWebsiteWarningModal] = useState(false);
	const [showConnectWebsiteModal, setShowConnectWebsiteModal] = useState(false);
	const failAlertRef = useRef<any>(null);

	// ------------------- NON-STATE CONSTANTS -------------------
	const isProduction = process.env.REACT_APP_DRF_DOMAIN === "https://api.abun.com";
	const defaultFeaturedImageURL: string = "https://res.cloudinary.com/diaiivikl/image/upload/v1690186987/" +
		"featured_image_1200x628.png"

	const defaultSlug: string = "what-is-draftss-com";
	const defaultMetaDescription: string = "Draftss.com offers on-demand graphic design, UI/UX, and coding through a flexible subscription model, ensuring fast turnaround for businesses and agencies.";

	// ------------------- STATES -------------------
	const [articlePosted, setArticlePosted] = useState(false);

	const [articlePostStatus, setArticlePostStatus] = useState("draft");

	const [integrationDone, setIntegrationDone] = useState(false);

	const [selectedIntegration, setSelectedIntegration] = useState("");

	const [editTitleModalActive, setEditTitleModalActive] = useState(false);

	const [processing, setProcessing] = useState(true);

	const [articleTitle, setArticleTitle] = useState("");

	const [keyword, setKeyword] = useState<string | null>(null);

	const [keywordTraffic, setKeywordTraffic] = useState<number | null>(null);

	const [wordCount, setWordCount] = useState<number | null>(null);

	const [featuredImageURL, setFeaturedImageURL] = useState<string | null>(null);

	const [articleContent, setArticleContent] = useState<string | undefined>(undefined);

	const [externalBacklinksPreference, setExternalBacklinksPreference] = useState<string>("");

	const [saveButtonText, setSaveButtonText] = useState("Save");

	const [disableSave, setDisableSave] = useState(true);

	const [disableUpdatePublish, setDisableUpdatePublish] = useState(true);

	const [changeTitleValue, setChangeTitleValue] = useState("");

	const [articleLanguagePreference, setArticleLanguagePreference] = useState("en");

	// article post link
	const [articlePostLink, setArticlePostLink] = useState<string>("");

	// article posted to (wordpress, wix, webflow etc.)
	const [articlePostedTo, setArticlePostedTo] = useState<string>("");

	// article posted on (date)
	const [articlePostedOn, setArticlePostedOn] = useState<string>("");

	const [articleScheduledForPosting, setArticleScheduledForPosting] = useState<boolean>(false);

	const [articleScheduledDatetime, setArticleScheduledDatetime] = useState<Dayjs | null>(null);

	const [keywordProjectId, setKeywordProjectId] = useState<string>("");

	const [locationIsoCode, setLocationIsoCode] = useState<string>("");

	const [keywordHash, setKeywordHash] = useState<string>("");

	const [articleMetaDescription, setArticleMetaDescription] = useState<string>("");

	const [articleURLSlug, setarticleURLSlug] = useState<string>("");


	const [schedulePublishDateTime, setSchedulePublishDateTime] = useState<Dayjs | null>(dayjs().add(1, "day"));

	// today's date and time in string format as default value
	const [schedulePublishDate, setSchedulePublishDate] = useState<string>(dayjs().format("YYYY-MM-DD"));
	const [schedulePublishTime, setSchedulePublishTime] = useState<string>(dayjs().format("HH:mm"));

	const [articleFeedback, setArticleFeedback] = useState<string>("no_feedback");

	const [selectedIntegrationUniqueID, setSelectedIntegrationUniqueID] = useState("");

	const [integrationWithUniqueID, setIntegrationWithUniqueID] = useState<Array<AllIntegrations>>([]);

	const [articleExternalLinks, setArticleExternalLinks] = useState<Array<Link>>([]);

	const [articleInternalLinks, setArticleInternalLinks] = useState<Array<Link>>([]);
	const [suggestedInternalLinks, setSuggestedInternalLinks] = useState<Array<SuggestedInternalLink>>([]);

	const [newLinkHref, setNewLinkHref] = useState<string>("https://");
	const [newLinkPhrase, setNewLinkPhrase] = useState<string>("");

	const [articleInternalLinkEditableIndex, setArticleInternalLinkEditableIndex] = useState<number | null>(null);
	const [articleExternalLinkEditableIndex, setArticleExternalLinkEditableIndex] = useState<number | null>(null);
	const [isSidebarCollapsed, setIsSidebarCollapsed] = useState<boolean>(false);
	const [isOptionSidebarCollapsed, setIsOptionSidebarCollapsed] = useState<boolean>(true);
	const [uploadFeaturedImageModalActive, setUploadFeaturedImageModalActive] = useState(false);
	const [addNewLinkModalActive, setAddNewLinkModalActive] = useState({ active: false, linkType: "internal" });
	const [modalText, setModalText] = useState("");
	const [requestModalActive, setRequestModalActive] = useState(false);
	const [isFeatureImageLoading, setIsFeatureImageLoading] = useState(false);
	const [aiEditorMarkdownContent, setAiEditorMarkdownContent] = useState<string | undefined>(undefined);
	const [aiEditorHtmlContent, setAiEditorHtmlContent] = useState<string | undefined>(undefined);
	const [aiEditorTextContent, setAiEditorTextContent] = useState<string | undefined>(undefined);
	const [publishOption, setPublishOption] = useState('publish');
	const [publishType, setPublishType] = useState('publish');
	const [copyAs, setCopyAs] = useState<string>("markdown");
	const [linkState, setLinkState] = useState(false);
	const [showUnsavedChangesModal, setShowUnsavedChangesModal] = useState(false);

	// states for categories in wordpress
	const [categories, setCategories] = useState<Array<Categories>>([]);
	const [checkedCategories, setCheckedCategories] = useState<number | null>(null);
	const [categoryName, setCategoryName] = useState("");
	const [categoryDescription, setCategoryDescription] = useState("");
	const [addNewCategoryModalActive, setAddNewCategoryModalActive] = useState({ active: false });
	const [isLoading, setIsLoading] = useState(false);
	const [currTab, setCurrTab] = useState<'article' | 'twya'>('article');
	const [twyaSelectedText, setTwyaSelectedText] = useState<string | null>(null);
	const [twyaCustomInstructions, setTwyaCustomInstructions] = useState<string | null>(null);
	const [newSearchImage, setNewSearchImage] = useState("")
	const [searchImageUrl, setSearchImageUrl] = useState<SearchImage[]>([])
	const [visibleCount, setVisibleCount] = useState(4);
	const [markdownContent, setMarkdownContent] = useState<string | undefined>(undefined);
	const IMAGES_PER_LOAD = 4;

	// ------------------- QUERIES -------------------
	const articleIsGenerated = useQuery(articleIsGeneratedQuery(articleUID as string));
	const loadArticleContent = useQuery(loadArticleContentQuery(articleUID as string));
	const saveSettings = useMutation(saveSettingsMutation);
	const [isEditingTitle, setIsEditingTitle] = useState(false);

	//Define Ref
	const articleEditorDivRef = useRef(null);
	const aiEditor = useRef<AiEditor | null>(null);
	const hasRestoredRef = useRef(false);

	// ------------------- CUSTOM EVENTS -------------------
	function GetWordCount(data: string): number {
		var pattern = /[a-zA-Z0-9_\u00A0-\u02AF\u0392-\u03c9\u0410-\u04F9]+|[\u4E00-\u9FFF\u3400-\u4dbf\uf900-\ufaff\u3040-\u309f\uac00-\ud7af]+/g;
		var m = data.match(pattern);
		var count = 0;
		if (m === null) return count;
		for (var i = 0; i < m.length; i++) {
			if (m[i].charCodeAt(0) >= 0x4E00) {
				count += m[i].length;
			} else {
				count += 1;
			}
		}
		return count;
	}

	function setIntegrationAndHideDropDownContent(integrate: string) {
		setSelectedIntegration(integrate);
	}

	function debounce<T extends (...args: any[]) => any>(
		func: T,
		wait: number
	): (...args: Parameters<T>) => void {
		let timeout: NodeJS.Timeout | undefined;

		return function (this: any, ...args: Parameters<T>): void {
			clearTimeout(timeout);
			timeout = setTimeout(() => func.apply(this, args), wait);
		};
	}

	function useMediaQuery(query: string) {
		const [matches, setMatches] = useState(() => window.matchMedia(query).matches);

		useEffect(() => {
			const mediaQueryList = window.matchMedia(query);
			const documentChangeHandler = () => setMatches(mediaQueryList.matches);

			mediaQueryList.addEventListener('change', documentChangeHandler);

			return () => {
				mediaQueryList.removeEventListener('change', documentChangeHandler);
			};
		}, [query]);

		return matches;
	};

	// ------------------- MUTATIONS -------------------
	const saveArticle = useMutation(saveArticleMutation);
	const postArticle = useMutation(postArticleMutation);
	const editTitle = useMutation(editTitleMutation);
	const scheduleArticle = useMutation(scheduleArticleMutation);
	const removeFeaturedImage = useMutation(removeFeaturedImageMutation);
	const generateNewFeaturedImage = useMutation(generateNewFeaturedImageMutation);
	const addCategory = useMutation(addCategoryMutation);
	const addGhlCategory = useMutation(addGhlCategoryMutation)
	const postCategoryChecked = useMutation(wpPostCategoryCheckedMutation);
	const twyaSubmit = useMutation(twyaSubmitMutation);
	const searchImage = useMutation(searchImageMutation)

	// ----------------------- REFS -----------------------
	const successAlertRef = useRef<any>(null);
	const errorAlertRef = useRef<any>(null);

	// ----------------------- MEDIA QUERIES -----------------------
	const isMediumScreen = useMediaQuery("(max-width: 1000px)");
	const isSmallScreen = useMediaQuery("(max-width: 600px)");

	// ----------------------- EFFECTS -----------------------
	useEffect(() => {
		if (articlePosted) {
			document.title = "View Article - Published";
		} else {
			document.title = "View Article - Not Published";
		}
	}, [articlePosted]);

	useEffect(() => {
		if (currTab === 'twya') {
			// set trigger for twya onSelectionChange
			document.addEventListener("selectionchange", (event) => {
				console.log("selection changed", event);
				setTwyaSelectedText(window.getSelection()?.toString() || null);
			});
		}
	}, [currTab]);

	useEffect(() => {
		const handleBackButton = (event) => {
			if (!disableSave) {
				setShowUnsavedChangesModal(true);
				event.preventDefault(); // Stop browser from navigating back
			}
		};

		window.addEventListener("popstate", handleBackButton);

		return () => {
			window.removeEventListener("popstate", handleBackButton);
		};
	}, [disableSave]);

	useEffect(() => {
		if (isMediumScreen) {
			setIsSidebarCollapsed(true);
		} else {
			setIsSidebarCollapsed(false);
		}
	}, [isMediumScreen]);

	useEffect(() => {
		if (hasRestoredRef.current || !aiEditorHtmlContent) return;

		const savedImages = localStorage.getItem("searchImageUrl");
		const savedSearchTerm = localStorage.getItem("newSearchImage");

		if (savedImages) {
			const parsedImages = JSON.parse(savedImages);
			const decodedEditorHtml = decodeHtmlEntities(aiEditorHtmlContent); // Fix encoding issue

			const isImageUsed = parsedImages.some((img: { url: string }) =>
				decodedEditorHtml.includes(img.url)
			);

			if (isImageUsed) {
				setSearchImageUrl(parsedImages);
				setNewSearchImage(savedSearchTerm ?? "");
			} else {
				localStorage.removeItem("searchImageUrl");
				localStorage.removeItem("newSearchImage");
			}
		}

		hasRestoredRef.current = true;
	}, [aiEditorHtmlContent]);

	// fire "load article" api if article is generated
	useEffect(() => {
		if (articleIsGenerated.data) {
			let responseData = (articleIsGenerated.data as any)['data'] as ArticleIsGenerated;

			setArticleTitle(responseData.title);
			setKeyword(responseData.keyword);
			setKeywordTraffic(responseData.traffic);
			setKeywordHash(responseData.keyword_hash);
			setKeywordProjectId(responseData.keyword_project_id);
			setLocationIsoCode(responseData.locationIsoCode ? responseData.locationIsoCode : "zz");

			if (responseData.generated) {
				loadArticleContent.refetch().then();
			}

			if (responseData.failed && keywordHash && keywordProjectId) {
				navigate(`/keyword-project/${keywordProjectId}/titles/${keywordHash}`);
			}

		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [articleIsGenerated.data]);

	useEffect(() => {
		if (loadArticleContent.data) {
			let responseData = (loadArticleContent.data as any)['data'] as GetArticleContentResponse;
			setArticlePosted(responseData.is_posted);
			setArticlePostStatus(responseData.article_post_status);
			setProcessing(responseData.processing);
			setMarkdownContent(responseData.content)
			setArticleContent(responseData.content);
			setExternalBacklinksPreference(responseData.external_backlinks_preference);
			setArticleTitle(responseData.title);
			setChangeTitleValue(responseData.title);
			setWordCount(responseData.word_count);
			setFeaturedImageURL(responseData.featured_image);
			setIntegrationDone(responseData.all_integrations_with_unique_id.length > 0)
			setArticleScheduledForPosting(responseData.article_scheduled_for_posting);
			setArticleScheduledDatetime(dayjs(responseData.article_scheduled_datetime));
			setSchedulePublishDateTime(dayjs(responseData.article_scheduled_datetime));
			setIntegrationWithUniqueID(responseData.all_integrations_with_unique_id);
			setArticleFeedback(responseData.article_feedback);
			setArticlePostLink(responseData.post_link);
			setPublishOption(responseData.article_scheduled_datetime ? "schedule" : "publish")
			setArticleLanguagePreference(convertArticleLanguagePreferenceToLanguageCode(responseData.article_language_preference));

			if (responseData.posted_on) {
				setArticlePostedOn(dayjs(responseData.posted_on).format("hh:mma DD MMMM YYYY"));
			}

			if (responseData.is_posted) {
				// Find the integration matching the posted_to value
				let matchingIntegration = responseData.all_integrations_with_unique_id.find((integration) =>
					integration.integrationName.toLowerCase().includes(responseData.post_link.toLowerCase())
				);

				if (!matchingIntegration) {
					matchingIntegration = responseData.all_integrations_with_unique_id.find((integration) =>
						integration.integrationName.toLowerCase().includes(responseData.posted_to.toLowerCase())
					);
				}

				// Set the selected integration and unique ID
				setSelectedIntegration(
					matchingIntegration ? matchingIntegration.integrationName : ""
				);
				setSelectedIntegrationUniqueID(
					matchingIntegration ? matchingIntegration.integrationUniqueID : ""
				);
			} else if (responseData.active_integration) {				
				// Find the integration with a matching name
				const matchedIntegration = responseData.all_integrations_with_unique_id.find(
					(integration) => integration.integrationName === responseData.active_integration
				);

				if (matchedIntegration) {
					setSelectedIntegration(matchedIntegration.integrationName);
					setSelectedIntegrationUniqueID(matchedIntegration.integrationUniqueID);
				} 
			} else {
				// Default to the first integration in the list if no specific integration is posted
				const firstIntegration = responseData.all_integrations_with_unique_id[0];
				setSelectedIntegration(firstIntegration ? firstIntegration.integrationName : "");
				setSelectedIntegrationUniqueID(firstIntegration ? firstIntegration.integrationUniqueID : "");
			}

			// set meta description
			setArticleMetaDescription(responseData.article_description ? responseData.article_description : "No meta data");

			// set article url slug
			setarticleURLSlug(responseData.url_slug);

			// set article posted
			setArticlePostedTo(responseData.posted_to);

			// set suggested internal links if available
			if (responseData.suggested_internal_links) {
				// Filter out links that already exist in the content
				const filteredLinks = filterExistingSuggestedLinks(responseData.content, responseData.suggested_internal_links);
				setSuggestedInternalLinks(filteredLinks);
			}
		}
	}, [loadArticleContent.data]);

	// Filter suggested links when article content changes
	useEffect(() => {
		if (articleContent?.trim().length && suggestedInternalLinks.length > 0) {
			const filteredLinks = filterExistingSuggestedLinks(articleContent, suggestedInternalLinks);
			if (filteredLinks.length !== suggestedInternalLinks.length) {
				setSuggestedInternalLinks(filteredLinks);
			}
		}
	}, [articleContent, suggestedInternalLinks.length]);

	useEffect(() => {
		if (markdownContent?.trim().length) {			
			analyzeAndUpdateArticleContent(markdownContent);
		}
		if (articleEditorDivRef.current && markdownContent) {
			aiEditor.current = new AiEditor({
				editable: true,
				element: articleEditorDivRef.current,
				placeholder: "Write your article here...",
				content: markdownContent,
				draggable: false,
				lang: articleLanguagePreference,
				toolbarKeys: [
					"heading",
					"bold",
					"italic",
					"underline",
					"strike",
					"link",
					"code",
					"divider",
					"divider",
					"font-color",
					"align",
					"bullet-list",
					"ordered-list",
					"hr",
					"divider",
					"image",
					"video",
					"quote",
					"code-block",
					"table",
					"source-code",
					"divider",
					// "ai"
				],
				textSelectionBubbleMenu: {
					enable: true,
					items: [
						"bold",
						"italic",
						"underline",
						"strike",
						"subscript",
						"code",
						"link",
						"ai"
					]
				},
				ai: {
					models: {
						openai: {
							model: "gpt-4o-mini"
						} as OpenaiModelConfig,
					},
					onCreateClientUrl: (_, modelConfig, startFn) => {
						makeApiRequest("/api/frontend/generate-ai-streaming-token/", "get", modelConfig).then(
							resp => resp.data
						).then(data => {
							startFn(`${process.env.REACT_APP_DRF_DOMAIN}/api/frontend/stream-ai-response/${data.token}/`)
						})
					},
				},
				link: {
					class: "",
					bubbleMenuItems: ["Edit", "UnLink"],
				},
				image: {
					bubbleMenuItems: ["AlignLeft", "AlignCenter", "AlignRight", "resize", "delete"]
				},
				onChange: (editor: AiEditor) => {
					setAiEditorMarkdownContent(aiEditor.current?.getMarkdown());
					setArticleContent(aiEditor.current?.getMarkdown());
					setAiEditorHtmlContent(aiEditor.current?.getHtml());
					setDisableSave(false);
				}
			}).changeLang(articleLanguagePreference);

			setAiEditorMarkdownContent(aiEditor.current.getMarkdown());
			setAiEditorHtmlContent(aiEditor.current.getHtml());

			const markdown = aiEditor.current.getMarkdown();

			// Use a regular expression to find all image markdown and replace it with the URL followed by a line break
			const imageRegex = /!\[.*?\]\((.*?)\)/g;
			let finalContent = markdown.replace(imageRegex, (match, imageUrl) => {
				return `${imageUrl}\n\n`; // Replace the image markdown with just the URL and a line break
			});

			const plainTextContent = removeMarkdown(finalContent);
			setAiEditorTextContent(plainTextContent);

			const observer = new MutationObserver(() => {
				const popup = document.querySelector(".aie-popover-content");
				if (popup) {
					const input = popup.querySelector("#href") as HTMLInputElement | null;
					if (input) {
						if (!input.value) {
							input.value = "https://"; // Set default value
						}

						// Ensure event listener is not attached multiple times
						if (!(input as HTMLElement).dataset.listenerAdded) {
							(input as HTMLElement).dataset.listenerAdded = "true";
							input.addEventListener("blur", () => {
							const scrollTop = window.scrollY;
							if (input.value && input.value !== "https://") {
								setLinkState(true);
							}
							setTimeout(() => {
								window.scrollTo({ top: scrollTop, behavior: "auto" });
							}, 100);
						});
						}
					}
				}
			});

			observer.observe(document.body, {
				childList: true,
				subtree: true,
			});

			const handleMouseOver = (event: MouseEvent) => {
				const target = event.target as HTMLAnchorElement;
				if (target.tagName === "A" && target.closest(".aie-content")) {
					const href = target.getAttribute("href") || "No URL";

					// Create tooltip element
					let tooltip = document.createElement("div");
					tooltip.innerText = href;
					tooltip.classList.add("link-tooltip");

					// Position the tooltip
					const rect = target.getBoundingClientRect();
					tooltip.style.top = `${rect.bottom + window.scrollY + 5}px`;
					tooltip.style.left = `${rect.left + window.scrollX}px`;
					tooltip.setAttribute("id", "link-tooltip");

					// Remove any existing tooltip
					document.getElementById("link-tooltip")?.remove();
					document.body.appendChild(tooltip);

					// Remove tooltip on mouse out
					target.addEventListener("mouseout", () => {
						tooltip.remove();
					}, { once: true });
				}
			};

			document.body.addEventListener("mouseover", handleMouseOver);

			return () => {
				aiEditor.current?.destroy();
				observer.disconnect();
				document.body.removeEventListener("mouseover", handleMouseOver);
			}
		}
	}, [markdownContent, articlePosted]);

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			const dropdowns = [
				document.querySelector(".copy-txt-dropdown"),
				document.querySelector(".publish-dropdown"),
				document.querySelector(".download-dropdown")
			];

			dropdowns.forEach(dropdown => {
				if (dropdown && !dropdown.contains(event.target as Node)) {
					dropdown.classList.remove("show");
				}
			});
		};

		document.addEventListener("mousedown", handleClickOutside);

		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, []);


	// < !---  Performing Read and update category logic in wordpress ---! >

	// Handle checkbox change: toggle between categories
	const handleCheckboxChange = (categoryId: number) => {
		setCheckedCategories((prevCheckedCategoryId) =>
			prevCheckedCategoryId === categoryId ? null : categoryId
		);
		setDisableSave(false);
	};

	function decodeHtmlEntities(html: string) {
		const txt = document.createElement("textarea");
		txt.innerHTML = html;
		return txt.value;
	}

	function openInNewTab(site){
		window.open(`https://${site}/`, "_blank");
	}



	// Get the WordPress URL if found
	const selectedWordpressUrl = selectedIntegration.includes("wordpress") ? selectedIntegrationUniqueID : null;
	const selectedGhlSite = selectedIntegration.includes("ghl") ? selectedIntegrationUniqueID : null;
	// Fetch Categories function (no parameters)
	const fetchCategories = async () => {
		try {
			const response = await makeApiRequest(
				'/api/frontend/wp-category-integration/',
				'post', // Method
				{ wp_site_url: selectedWordpressUrl }
			);

			if (response.status !== 200) {
				throw new Error('Failed to fetch categories');
			}

			const data = await response.data;
			if (data.success) {
				setCategories(data.categories);  // Update the state with fetched categories
			}
		} catch (error) {
			console.error('Error fetching categories:', error);
		}
	};

	const fetchGhlCategories = async () => {
		try {
			const response = await makeApiRequest(
				'/api/frontend/get-ghl-categories/',
				'post', // Method
				{ ghl_site_id: selectedGhlSite }
			);

			if (response.status !== 200) {
				throw new Error('Failed to fetch categories');
			}

			const data = await response.data;
			if (data.success) {
				setCategories(data.categories);  // Update the state with fetched categories
			}
		} catch (error) {
			console.error('Error fetching categories:', error);
		}
	};

	useEffect(() => {
		// Fetch categories initially
		if (selectedIntegration.includes("wordpress")) {
			fetchCategories();
		}
	}, [selectedWordpressUrl]);

	useEffect(() => {
		// Fetch categories initially
		if (selectedIntegration.includes("ghl")) {
			setCategories([])
			fetchGhlCategories();
		}
	}, [selectedGhlSite]);

	//Add Category from frontend
	const addCategoryHandler = async () => {
		errorAlertRef.current?.close();
		successAlertRef.current?.close();

		// Set loading state and show processing modal if applicable
		setIsLoading(true);

		// Trigger the mutation
		addCategory.mutate(
			{
				wpSiteUrl: selectedWordpressUrl ?? '',
				name: categoryName,
				description: categoryDescription,
			},
			{
				onSuccess: (response) => {
					if (response.data.success) {
						successAlertRef.current?.show("New Category added successfully.");
						setAddNewCategoryModalActive({ active: false });

						// Optionally, clear the form fields
						setCategoryName('');
						setCategoryDescription('');

						// Immediately fetch categories after category is created
						fetchCategories();
					} else {
						throw new Error("Failed to add category");
					}
				},
				onError: () => {
					errorAlertRef.current?.show(
						"Adding categories is not supported. Please add them directly from the GHL website."
					);
					setTimeout(() => {
						errorAlertRef.current?.close();
					}, 5000);
				},
				onSettled: () => {
					setIsLoading(false);
				},
			}
		);
	};

	const addGhlCategoryHandler = async () => {
		errorAlertRef.current?.close();
		successAlertRef.current?.close();

		// Set loading state and show processing modal if applicable
		setIsLoading(true);

		// Trigger the mutation
		addGhlCategory.mutate(
			{
				ghlSiteId: selectedGhlSite ?? '',
				name: categoryName,
			},
			{
				onSuccess: (response) => {
					if (response.data.success) {
						successAlertRef.current?.show("New Category added successfully.");
						setAddNewCategoryModalActive({ active: false });

						// Optionally, clear the form fields
						setCategoryName('');

						// Immediately fetch categories after category is created
						fetchGhlCategories();
					} else {
						throw new Error("Failed to add category");
					}
				},
				onError: () => {
					errorAlertRef.current?.show(
						"Oops! Something went wrong. Please try again or contact support if the issue persists."
					);
					setTimeout(() => {
						errorAlertRef.current?.close();
					}, 5000);
				},
				onSettled: () => {
					setIsLoading(false);
				},
			}
		);
	};

	const searchImageHandler = async () => {
		errorAlertRef.current?.close();
		successAlertRef.current?.close();

		// Set loading state and show processing modal if applicable

		// Trigger the mutation
		searchImage.mutate(
			{
				searchKeyword: newSearchImage
			},
			{
				onSuccess: (response) => {
					if (response.data.status === "success") {
						if (response.data.images.length > 0) {
							setSearchImageUrl(response.data.images)
							localStorage.setItem("newSearchImage", newSearchImage);
							localStorage.setItem("searchImageUrl", JSON.stringify(response.data.images));
							successAlertRef.current?.show("Successfully fetched searched Images.");
						} else {
							errorAlertRef.current?.show("No image found for searched Keyword.");
						}
					} else {
						throw new Error("Failed to search images");
					}
				},
				onError: () => {
					errorAlertRef.current?.show(
						"Oops! Something went wrong. Please try again or contact support if the issue persists."
					);
					setTimeout(() => {
						errorAlertRef.current?.close();
					}, 5000);
				},
				onSettled: () => {
				},
			}
		);
	}
	// This function is triggered when the user clicks the Save button
	const PostCheckedCategory = async () => {
		errorAlertRef.current?.close();
		successAlertRef.current?.close();

		// Set loading state and show processing modal if applicable
		setIsLoading(true);

		// Trigger the mutation to save category data
		postCategoryChecked.mutate(
			{
				articleTitle: articleTitle ?? '',  // Article title
				categoryId: checkedCategories ?? 0,  // Selected category ID
				selectedIntegration: selectedIntegration,
			},
			{
				onSuccess: (response) => {
					if (response.data.success) {
						console.log('Category saved successfully.');
					} else {
						throw new Error('Failed to save category');
					}
				},
				onError: () => {
					errorAlertRef.current?.show(
						'Oops! Something went wrong. Please try again or contact support if the issue persists.'
					);
					setTimeout(() => {
						errorAlertRef.current?.close();
					}, 5000);
				},
				onSettled: () => {
					setIsLoading(false); // Reset the loading state
				},
			}
		);
	};

	const convertArticleLanguagePreferenceToLanguageCode = (language: string) => {
		switch (language.toLowerCase()) {
			case "chinese":
				return "zh";

			case "english":
				return "en";

			case "german":
				return "de";

			case "portuguese":
				return "pt";

			case "spanish":
				return "es";

			case "hindi":
				return "hi";

			case "indonesian":
				return "id";

			case "japanese":
				return "ja";

			case "korean":
				return "ko";

			case "thai":
				return "th";

			case "vietnamese":
				return "vi";

			default:
				return "en";
		}
	}

	useEffect(() => {
		if (selectedWordpressUrl && articleTitle) {
			setIsLoading(true); // Set loading state before fetching

			const fetchCategory = async () => {
				try {
					const response = await makeApiRequest(
						'/api/frontend/wp-get-category-checked/', // API endpoint
						'get', // Method
						{ article_title: articleTitle } // Query parameter
					);

					const data = await response.data;

					if (data.success) {
						// If category is found, set the category ID
						setCheckedCategories(data.category_id);
					} else {
						// If no category is found, set to "Uncategorized"
						const uncategorizedCategory = categories.find(
							(category) => category.name === 'Uncategorized'
						);
						if (uncategorizedCategory) {
							setCheckedCategories(uncategorizedCategory.id);
						}
					}
				} catch (error) {
					// Handle error fetching the category
					errorAlertRef.current?.show(
						'Oops! Something went wrong. Please try again or contact support if the issue persists.'
					);
					setTimeout(() => {
						errorAlertRef.current?.close();
					}, 5000);
				} finally {
					setIsLoading(false); // Reset the loading state
				}
			};

			fetchCategory();
		}
	}, [articleTitle, categories, selectedWordpressUrl]);

	useEffect(() => {
		if (selectedGhlSite && articleTitle) {
			setIsLoading(true); // Set loading state before fetching

			const fetchGhlCategory = async () => {
				try {
					const response = await makeApiRequest(
						'/api/frontend/ghl-get-category-checked/', // API endpoint
						'get', // Method
						{ article_title: articleTitle } // Query parameter
					);

					const data = await response.data;

					if (data.success) {
						// If category is found, set the category ID
						setCheckedCategories(data.category_id);
					} else {
						if (categories.length === 0) {
							// If categories array is empty, set to null
							setCheckedCategories(null);
						} else {
							// Set the first category as default if available
							setCheckedCategories(categories[0].id);
						}
					}
				} catch (error) {
					// Handle error fetching the category
					errorAlertRef.current?.show(
						'Oops! Something went wrong. Please try again or contact support if the issue persists.'
					);
					setTimeout(() => {
						errorAlertRef.current?.close();
					}, 5000);
				} finally {
					setIsLoading(false); // Reset the loading state
				}
			};

			fetchGhlCategory();
		}
	}, [articleTitle, categories, selectedGhlSite]);

	// =================================================
	// ------------------- MAIN CODE -------------------
	// =================================================

	const updateArticleContent = (content: string) => {
		if (linkState) {
			setArticleContent(content);
			setLinkState(false)
		}
		analyzeAndUpdateArticleContent(content);
		//setAiEditorMarkdownContent(content);
		// setAiEditorHtmlContent(aiEditor.current?.getHtml() || '');
		// if (aiEditor.current && aiEditor.current.getMarkdown() !== content) {
		// 	aiEditor.current.setContent(content);
		// }
	};

	// Debounced version of updateArticleContent with a 5sec delay
	const debouncedUpdateContent = debounce(updateArticleContent, 5000);

	const handleContentChange = (e) => {
		const newContent = e.currentTarget.innerHTML;
		const updatedArticleContent = aiEditor.current?.getMarkdown() || newContent;
		debouncedUpdateContent(updatedArticleContent);  // Use debounced update function
	};

	const handleSaveClick = async () => {
		if (articleUID && articleContent) {
			try {
				saveContent(articleContent, articleMetaDescription, articleFeedback);

				if (selectedWordpressUrl || selectedGhlSite) {
					PostCheckedCategory();
				}

			} catch (error) {
				console.error('Failed to save content:', error);
			}
		}
	};

	const handleUpdateArticle = async () => {
		if (articleUID) {
			postToBlogHandler(articleUID, true);
			setDisableUpdatePublish(true);
		}

	};

	const handleUpdateArticleSave = async () => {
		if (articleUID && articleContent) {
			try {
				saveContent(articleContent, articleMetaDescription, articleFeedback);

				if (selectedWordpressUrl || selectedGhlSite) {
					PostCheckedCategory();
				}

			} catch (error) {
				console.error('Failed to save content:', error);
			}
		}
	}

	const normalizeUrl = (url: string): string => {
		try {
			const normalized = new URL(url.includes('://') ? url : `https://${url}`);
			return normalized.hostname.toLowerCase(); // removes protocol, ports, paths
		} catch {
			return ''; // Invalid URLs are ignored
		}
		};

	const getUrlPath = (url: string): string => {
	try {
		const normalized = new URL(url.includes('://') ? url : `https://${url}`);
		return normalized.pathname;
	} catch {
		return '';
	}
	};

	function handleIntegartionChange(integrationUniqueID: string, integrationName: string) {          

        setSelectedIntegrationUniqueID(integrationUniqueID);
		setIntegrationAndHideDropDownContent(integrationName);
				
        // Trigger save settings immediately
        saveSettings.mutate({
            settingsToSave: [
                { settingName: 'active_integration', settingValue: integrationName },                
            ]
        }, {
            onSuccess: () => {                
                successAlertRef.current.show("Changes Saved!");
                setTimeout(() => {
                    try {
                        if (successAlertRef.current) {
                            successAlertRef.current.close();
                        }
                    } catch (e) { }
                }, 3000);
            },
            onError: () => {
                errorAlertRef.current?.show("Oops! Something went wrong :( Please try again later or contact us for further support.");
            }
        });
      }

	function analyzeAndUpdateArticleContent(articleContent: string) {		
		setWordCount(GetWordCount(articleContent));

		// Regular expression to match href attributes within <a> tags
		const hrefRegex = /href=(["'])(.*?)\1/g;
		const links = articleContent.matchAll(hrefRegex);

		// Regular expression to match src attributes within <img> tags
		const imgSrcREgex = /<img\s+(?:[^>]*?\s+)?src=(["'])(.*?)\1/g;
		const imageTags = articleContent.matchAll(imgSrcREgex);
		let imagesNo = 0;
		let externalLinks = 0;
		let internalLinks = 0;

		// Regular expression to match Markdown-style links [visit our site](https://abun.com/)
		const markdownLinkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
		const markdownLinks = articleContent.matchAll(markdownLinkRegex);

		// Regular expression to match Markdown-style image syntax ![]()
		const markdownImgRegex = /!\[.*?\]\((.*?)\)/g;
		const markdownImages = articleContent.matchAll(markdownImgRegex);

		for (const link of links) {

			const href = link[2];

			if (href.includes("unsplash")) continue;//don't count unsplash img credit links

			if (href && !href.includes(active_website_domain as any)) {
				externalLinks++;
			} else if (href && href.includes(active_website_domain as any)) {
				internalLinks++;
			}
		}

		for (const markdownLink of markdownLinks) {
			const href = markdownLink[2];
			if (href && !href.includes(active_website_domain as any)) {
				externalLinks++;
			} else if (href && href.includes(active_website_domain as any)) {
				internalLinks++;
			}
		}

		for (const image of imageTags) {
			const src = image[2];
			if (src) {
				imagesNo++;
			}
		}

		for (const markdownImage of markdownImages) {
			const src = markdownImage[1];
			if (src) {
				imagesNo++;
			}
		}

		const articleExternalLinks: Link[] = [];
		const articleInternalLinks: Link[] = [];

		// Regular expression to match entire <a> tags and Markdown-style links
		const linkRegex = /<a\s+(?:[^>]*?\s+)?href="([^"]+)"(?:\s+rel="([^"]+)")?[^>]*>(.*?)<\/a>|\[([^\]]+)\]\(([^)]+)\)/g;

		for (const match of articleContent.matchAll(linkRegex)) {
			const linkHref = match[1] || match[5]; // Check which group matched for href
			const linkTxt = match[3]?.replaceAll("*", "") || match[4]?.replaceAll("*", ""); // Check which group matched for link text			

			const rel = match[2]; // Check for rel attribute
			
			// Skip unsplash image credit links and anchor links (starting with #)
			if (linkHref.includes("unsplash") || linkHref.startsWith("#")) continue;

			// const normalizedHref = linkHref.toLowerCase();
			const normalizedDomain = normalizeUrl(active_website_domain as string);
			const normalizedHref = normalizeUrl(linkHref);
			const hrefPath = getUrlPath(linkHref);

			const isSameDomain = normalizedHref === normalizedDomain;

			if (!hrefPath || hrefPath === '/' || hrefPath.trim() === '') {
				continue;
			}

			const link: Link = { linkHref, linkTxt };

			// Add rel attribute if it exists
			if (rel) {
				link.rel = rel;
			}

			 if (isSameDomain) {
				articleInternalLinks.push(link);
			} else {				
				articleExternalLinks.push(link);
			}
		}

		setArticleExternalLinks(articleExternalLinks);
		setArticleInternalLinks(articleInternalLinks);
	}

	function getTabId() {
		return sessionStorage.getItem('tabId');
	}

	function getPreviousPath() {
		const tabId = getTabId();
		const stackKey = `${tabId}-historyStack`;
		let stack = JSON.parse(sessionStorage.getItem(stackKey) || '[]');

		if (stack.length <= 1) return null;

		stack.pop(); // remove current path
		const previousPath = stack.pop(); // previous page
		sessionStorage.setItem(stackKey, JSON.stringify(stack));

		return previousPath;
	}

	const goBack = () => {
		if (!disableSave) {
		setShowUnsavedChangesModal(true);
		return;
		}

		const prev = getPreviousPath();
		if (prev) {
		navigate(prev);
		} else {
		navigate('/');
		}
	};

	function getIntegrationName(integration: string) {
		if (integration.includes("wordpress")) {
			return "wordpress";
		} else if (integration.includes("webflow")) {
			return "webflow";
		} else {
			return "wix";
		}
	}

	// optionally accepts updatedArticleContent and updatedArticleMetaDescription
	function saveContent(updatedArticleContent?: string, updatedArticleMetaDescription?: string, updatedArticleFeedback?: string) {
		if (articleContent && articleUID && articleMetaDescription && !processing) {
			setDisableSave(true);
			setSaveButtonText("Saving...");
			saveArticle.mutate({
				article_uid: articleUID,
				article_content: aiEditor.current?.getMarkdown() as string,
				article_description: updatedArticleMetaDescription ? updatedArticleMetaDescription : articleMetaDescription,
				article_feedback: updatedArticleFeedback ? updatedArticleFeedback : articleFeedback,
				url_slug: articleURLSlug
			}, {
				onSuccess: () => {
					setSaveButtonText("Save");
					setDisableSave(true);
					setDisableUpdatePublish(false)
					successAlertRef.current?.show("Article saved successfully.");
					setTimeout(() => {
						successAlertRef.current?.close();
					}, 3000);
				},
				onError: () => {
					setSaveButtonText("Save");
					setDisableSave(false);
					setDisableUpdatePublish(true)
					errorAlertRef.current?.show("Failed to save article. Please try again.");
					setTimeout(() => {
						errorAlertRef.current?.close();
					}, 3000);
				}
			});
		}
	}

	function removeLinkHandler(link: Link) {
		// if (articlePosted) {
		// 	errorAlertRef.current?.show("You cannot remove links from a published article.");
		// 	setTimeout(() => {
		// 		errorAlertRef.current?.close();
		// 	}, 5000);
		// 	return;
		// }
		// Escape special characters(., *, +, ?, ^, $, {, }, [, ], (, ), \, |, /)
		// in linkTxt for the regex pattern

		const escapedLinkTxt = link.linkTxt.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
		const aTagRegex = new RegExp(`<a\\s+href="${link.linkHref}"(?:\\s+rel="[^"]*")?>${escapedLinkTxt}<\\/a>`, 'g');
		// Replace the <a> tag with the plain text
		let updatedArticleContent = articleContent?.replace(aTagRegex, link.linkTxt);

		// Regex pattern for markdown link []() types
		const markdownLinkRegex = new RegExp(`(?:!\\[)?\\[\\s*(\\*\\*\\s*)?${escapedLinkTxt}(\\s*\\*\\*)?\\s*\\]\\(${link.linkHref}\\)`, 'g');
		updatedArticleContent = updatedArticleContent?.replace(markdownLinkRegex, link.linkTxt + " ");

		// Regex pattern for <img> tags to replace with their src attribute URLs
		const imgTagRegex = /<img[^>]+src="([^">]+)"[^>]*>/g;
		updatedArticleContent = updatedArticleContent?.replace(imgTagRegex, (match) => {
			return match;
		});

		aiEditor.current?.setContent(updatedArticleContent || "");
		setArticleContent(updatedArticleContent);
		saveContent(updatedArticleContent);		
		analyzeAndUpdateArticleContent(updatedArticleContent as string);
		setDisableSave(false);
		setLinkState(true)
	}

	// Function to filter out suggested links that already exist in the article content
	function filterExistingSuggestedLinks(content: string, suggestedLinks: SuggestedInternalLink[]): SuggestedInternalLink[] {
		if (!content || !suggestedLinks.length) return suggestedLinks;

		// Extract existing links from content using the same regex patterns as analyzeAndUpdateArticleContent
		const existingLinks: string[] = [];

		// Regular expression to match entire <a> tags and Markdown-style links
		const linkRegex = /<a\s+(?:[^>]*?\s+)?href="([^"]+)"(?:\s+rel="([^"]+)")?[^>]*>(.*?)<\/a>|\[([^\]]+)\]\(([^)]+)\)/g;

		for (const match of content.matchAll(linkRegex)) {
			const linkHref = match[1] || match[5]; // Check which group matched for href

			// Skip unsplash image credit links and anchor links (starting with #)
			if (linkHref && !linkHref.includes("unsplash") && !linkHref.startsWith("#")) {
				existingLinks.push(linkHref);
			}
		}

		// Filter out suggested links that already exist in the content
		return suggestedLinks.filter(suggestedLink =>
			!existingLinks.includes(suggestedLink.link)
		);
	}

	function addSuggestedLinkHandler(suggestedLink: SuggestedInternalLink) {
		if (!articleContent) return;

		// Escape special characters in the phrase for regex
		const escapedPhrase = suggestedLink.phrase.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

		// Create regex to find the phrase in the content
		const phraseRegex = new RegExp(`\\b${escapedPhrase}\\b`, 'g');

		// Check if the phrase exists in the content
		if (!articleContent.match(phraseRegex)) {
			errorAlertRef.current?.show("The suggested phrase was not found in the article content.");
			setTimeout(() => {
				errorAlertRef.current?.close();
			}, 5000);
			return;
		}

		// Replace the phrase with a markdown link
		const markdownLink = `[${suggestedLink.phrase}](${suggestedLink.link})`;
		const updatedContent = articleContent.replace(phraseRegex, markdownLink);

		// Update the article content
		aiEditor.current?.setContent(updatedContent || "");
		setArticleContent(updatedContent);	
		analyzeAndUpdateArticleContent(updatedContent);
		setDisableSave(false);
		setLinkState(true);

		// Remove the added link from suggested links list
		setSuggestedInternalLinks(prevLinks =>
			prevLinks.filter(link =>
				!(link.phrase === suggestedLink.phrase && link.link === suggestedLink.link)
			)
		);

		// Show success message
		successAlertRef.current?.show("Suggested link added successfully.");
		setTimeout(() => {
			successAlertRef.current?.close();
		}, 3000);
	}


	// for saving link after editing url
	function saveEditedLinkHandler(linkType: "internal" | "external", linkToBeReplaced: Link, newLinkUrl: string) {
		if (articlePosted) {
			errorAlertRef.current?.show("You cannot edit links in a published article.");
			setTimeout(() => {
				errorAlertRef.current?.close();
			}, 5000);
			return;
		}
		// Check if new url is an HTML tag
		const htmlTagRegex = /<[^>]*>/g;
		if (htmlTagRegex.test(newLinkUrl as string)) {
			errorAlertRef.current?.show(`${linkType} link URL cannot be an HTML tag.`);
			setTimeout(() => {
				errorAlertRef.current?.close();
			}, 5000);
			return;
		}

		// Check if new url is in valid URL format
		const urlPattern = /^(https?|ftp):\/\/[^\s/$.?#]+\.[^\s]*$/i;
		if (!urlPattern.test(newLinkUrl as string)) {
			errorAlertRef.current?.show(`Please enter a valid ${linkType} link URL.`);
			setTimeout(() => {
				errorAlertRef.current?.close();
			}, 5000);
			return;
		}

		//---------------- Replace the link ------------
		// Replace the link url of the <a></a> type link and []() with newLinkUrl

		let updatedArticleContent: string;

		if (linkToBeReplaced?.rel) {// if it had rel attribute, for external links
			updatedArticleContent = (articleContent as string).replace(
				`<a href="${linkToBeReplaced?.linkHref}" rel="${linkToBeReplaced.rel}">${linkToBeReplaced?.linkTxt}</a>`,
				`<a href="${newLinkUrl}" rel="${linkToBeReplaced.rel}">${linkToBeReplaced?.linkTxt}</a>`)
		} else {
			updatedArticleContent = (articleContent as string).replace(
				`<a href="${linkToBeReplaced?.linkHref}">${linkToBeReplaced?.linkTxt}</a>`,
				`<a href="${newLinkUrl}">${linkToBeReplaced?.linkTxt}</a>`
			).replace(
				`[${linkToBeReplaced?.linkTxt}](${linkToBeReplaced?.linkHref})`,
				`[${linkToBeReplaced?.linkTxt}](${newLinkUrl})`
			);
		}

		aiEditor.current?.setContent(updatedArticleContent || "");
		setArticleContent(updatedArticleContent);
	}

	//Connecting Internal Website
	function ConnectWebsite(props: ConnectWebsiteProps) {
		// -------------------- STATES --------------------
		const [stage, setStage] = useState(1);  // start from first stage
		const [called, setCalled] = useState(false);

		// -------------------- CONSTANTS --------------------
		const navigate = useNavigate();
		const stages = [
			<p>Oops! Something went wrong :(</p>,
			<WebsiteDomain getWebsiteData={getWebsiteData} updateWebsiteData={updateWebsiteData} nextStage={nextStage} getWebsiteTempData={getWebsiteTempData} updateWebsiteTempData={updateWebsiteTempData} />,  // domain & blog
			<WebsiteTitle getWebsiteData={getWebsiteData} updateWebsiteData={updateWebsiteData} nextStage={nextStage} getWebsiteTempData={getWebsiteTempData} updateWebsiteTempData={updateWebsiteTempData} setStage={setStage} />, // title & description
			<WebsiteICP getWebsiteData={getWebsiteData} updateWebsiteData={updateWebsiteData} nextStage={nextStage} getWebsiteTempData={getWebsiteTempData} updateWebsiteTempData={updateWebsiteTempData} setStage={setStage} />,  // icp text & industry
			<WebsiteCompetitors getWebsiteData={getWebsiteData} updateWebsiteData={updateWebsiteData} nextStage={nextStage} setStage={setStage} />  // competitors
		];

		// -------------------- REFS --------------------
		const connectWebsiteData = useRef<WebsiteData>({
			domain: '',
			protocol: '',
			blog: '',
			title: '',
			description: '',
			industry: '',
			icp: '',
			language: '',
			competitors: [],
			generate_content_plan: false,
		});

		// Temporary Website Data this used when user go back to previous steps
		const websiteTempData = useRef<WebsiteData>({
			domain: '',
			protocol: '',
			blog: '',
			title: '',
			description: '',
			industry: '',
			icp: '',
			language: '',
			competitors: [],
			generate_content_plan: false,
		});

		const {
			data,
			refetch,
			status,
			fetchStatus
		} = useQuery(connectWebsiteQuery(connectWebsiteData.current));

		function getWebsiteData(): WebsiteData {
			return connectWebsiteData.current;
		}

		function updateWebsiteData(key: keyof WebsiteData, value: any) {
			if (key !== 'generate_content_plan') connectWebsiteData.current[key] = value;
		}

		function getWebsiteTempData(): WebsiteData {
			return websiteTempData.current;
		}

		function updateWebsiteTempData(key: keyof WebsiteData, value: any) {
			if (key !== 'generate_content_plan') websiteTempData.current[key] = value;
		}

		/**
		 * Use to move to next stage.
		 */
		function nextStage() {
			let newStage = stage + 1;
			if (newStage < stages.length) {
				setStage(stage + 1);
			} else {
				// Last stage is done. Time to call connect website api.
				if (connectWebsiteData.current.competitors.length > 0 && !called) {
					refetch().then();
					setCalled(true);
				}
			}
		}

		useEffect(() => {
			if (status === 'success' && called) {
				props.setShowConnectWebsiteModal(false);
				setAddNewLinkModalActive({ active: true, linkType: "internal" });
				props.successAlertRef.current?.show("Website connected successfully.");
				setTimeout(() => {
					props.successAlertRef.current?.close();
				}, 5000);
			}
		}, [called, navigate, props, status]);


		return (
			<>
				<div className={"modal connect-website-modal is-active"}>
					<div className={"modal-background"}></div>
					<div className={"modal-content"}>
						<div className={"modal-card"}>
							<header className={"modal-card-head is-flex is-align-items-center is-justify-content-space-between"}>
								<p className={"modal-card-title"} style={{ opacity: 0, width: 0, height: 0, pointerEvents: 'none' }} />
								<p className={"modal-card-title"}>
									Find Competitors
								</p>
								{ // show only if stage is not 0
									stage <= 1 && <button type="button" className={"delete"} aria-label="close" onClick={() => props.setShowConnectWebsiteModal(false)} />
								}
							</header>
							<section className={"modal-card-body"}>
								<div className="container is-flex is-flex-direction-column is-align-items-center" style={{ maxHeight: "100vh", overflow: "none" }}>
									{fetchStatus !== 'idle' && stage !== stages.length - 1 ? <ConnectWebsiteLoading text={"Finishing setup. Just a moment..."} /> : stages[stage]}
								</div>
							</section>
						</div>
					</div>
				</div>
			</>
		)
	}

	function stripMarkdown(text: string) {
	return text
		.replace(/!\[.*?\]\(.*?\)/g, "") // remove images
		.replace(/\[([^\]]+)\]\(([^)]+)\)/g, "$1") // remove links but keep text
		.replace(/`([^`]+)`/g, "$1") // inline code
		.replace(/[*_]{1,3}([^*_]+)[*_]{1,3}/g, "$1") // bold/italic
		.replace(/~{2}([^~]+)~{2}/g, "$1") // strikethrough
		.replace(/^>+\s?/gm, "") // blockquotes
		.replace(/^#+\s(.+)/gm, "$1") // headers
		.replace(/[-*_]{3,}/g, "") // horizontal rules
		.replace(/\n{2,}/g, "\n") // collapse newlines
		.trim();
}


	// for adding new link
	function addNewLinkHandler(linkType: "internal" | "external") {
		// if (articlePosted) {
		// 	errorAlertRef.current?.show("You cannot add links to a published article.");
		// 	setTimeout(() => {
		// 		errorAlertRef.current?.close();
		// 	}, 5000);
		// 	return;
		// }		
		if (!newLinkHref.trim().length || !newLinkPhrase.trim().length) {
			return;
		}

		// ---------------- validations --------------------

		// Check if newLinkPhrase is an HTML tag
		const htmlTagRegex = /<[^>]*>/g;
		if (htmlTagRegex.test(newLinkPhrase)) {
			errorAlertRef.current?.show("Link phrase cannot be an HTML tag.");
			setTimeout(() => {
				errorAlertRef.current?.close();
			}, 5000);
			return;
		}

		// Check if newLinkHref is an HTML tag
		if (htmlTagRegex.test(newLinkHref)) {
			errorAlertRef.current?.show("Link URL cannot be an HTML tag.");
			setTimeout(() => {
				errorAlertRef.current?.close();
			}, 5000);
			return;
		}

		// Check if newLinkPhrase is too small or too long
		if (newLinkPhrase.split(" ").length < 2 || newLinkPhrase.split(" ").length > 12) {
			errorAlertRef.current?.show("Please enter a phrase that is 2 to 12 words long.");
			setTimeout(() => {
				errorAlertRef.current?.close();
			}, 5000);
			return;
		}

		// Check if newLinkHref is valid URL format
		const urlPattern = /^(https?|ftp):\/\/[^\s/$.?#]+\.[^\s]*$/i;
		if (!urlPattern.test(newLinkHref)) {
			errorAlertRef.current?.show("Please enter a valid link URL.");
			setTimeout(() => {
				errorAlertRef.current?.close();
			}, 5000);
			return;
		}

		const plainArticleContent = stripMarkdown(articleContent || "").replace(/\s+/g, " ");
		if (!plainArticleContent.includes(newLinkPhrase)) 
			{
			errorAlertRef.current?.show(`The phrase is not found in the article content.`);
			setTimeout(() => {
				errorAlertRef.current?.close();
			}, 5000);
			return;
		}

		// Check if newLinkPhrase is part of any heading
		const headingRegex = /\n\n#{1,6}\s.*?\n\n/g;
		const headings = articleContent?.match(headingRegex) || [];
		const isInHeading = headings.some((heading) => heading.includes(newLinkPhrase));
		if (isInHeading) {
			// Phrase is part of a heading, do not replace
			errorAlertRef.current?.show(`The phrase is part of a heading. Please enter another phrase or insert the link using editor.`);
			setTimeout(() => {
				errorAlertRef.current?.close();
			}, 5000);
			return;
		}

		// regex pattern to match newLinkPhrase(excludes in titles/headings)
		const phraseRegex = new RegExp(`(?<!##\\s)\\b${newLinkPhrase}\\b(?!\\s##)`, "gi");

		// Match occurrences of newLinkPhrase in the article content
		const phraseMatches = articleContent?.match(phraseRegex);
		if (phraseMatches && phraseMatches.length > 1) {
			errorAlertRef.current?.show(`The phrase appears multiple times. Please add more words around it or use the editor to insert the link.`);
			setTimeout(() => {
				errorAlertRef.current?.close();
			}, 5000);
			return;
		}

		// check if the newLinkPhrase is already a link text or part of any link text
		// for <a></a> tags
		const linkTextsFromATags: string[] = [];
		const linkRegexForATags = new RegExp(/<a\s+(?:[^>]*?\s+)?href="([^"]+)"[^>]*>(.*?)<\/a>/g);
		let match: RegExpExecArray | null;
		while ((match = linkRegexForATags.exec(articleContent as string)) !== null) {
			linkTextsFromATags.push(match[2]);
		}

		// for []() type of links
		const linkTextsFromMarkdownLinks: string[] = [];
		const markdownLinkRegex = new RegExp(/\[(.*?)\]\((.*?)\)/g);
		while ((match = markdownLinkRegex.exec(articleContent as string)) !== null) {
			linkTextsFromMarkdownLinks.push(match[1]);
		}

		const allLinkTexts = [...linkTextsFromATags, ...linkTextsFromMarkdownLinks];

		let isPhraseMatched = false;
		for (const text of allLinkTexts) {
			if (text === newLinkPhrase || text.includes(newLinkPhrase)) {
				isPhraseMatched = true;
				break;
			}
		}

		if (isPhraseMatched) {
			errorAlertRef.current?.show(`The phrase is already a link or part of a link, please edit its URL from the ${linkType} links tab or add the link using editor.`);
			setTimeout(() => {
				errorAlertRef.current?.close();
			}, 5000);
			return;
		}


		// ---------------- ADD THE LINK --------------------
		// Add link by replacing the text with an anchor tag after all validations are passed
		const normalizedNewHref = normalizeUrl(newLinkHref);
		const normalizedActiveDomain = normalizeUrl(active_website_domain as string);
		const hrefPath = getUrlPath(newLinkHref);
		let linkWasAdded = false;
		let updatedArticleContent = articleContent;
		
		if (hrefPath && hrefPath !== '/') {			
			const flexiblePhraseRegex = new RegExp(
				newLinkPhrase
					.split(/\s+/)
					.map(word => `[*_]*${word}[*_]*`)
					.join("\\s+"),
				"i"
			);

			let linkToAdd = `<a href="${newLinkHref}">${newLinkPhrase}</a>`;
			
			if ((
				externalBacklinksPreference === "no-follow" ||
				externalBacklinksPreference === "follow")
				&& normalizedNewHref !== normalizedActiveDomain // external link
			) {				
				const preference = externalBacklinksPreference.replace("-", "");
				//if its an external link and externalBacklinksPreference is set, add new link accordingly
				linkToAdd = `<a href="${newLinkHref}" rel="${preference}">${newLinkPhrase}</a>`;
			}			

			updatedArticleContent = articleContent?.replace(flexiblePhraseRegex, linkToAdd);
			linkWasAdded = true;
		}		
		aiEditor.current?.setContent(updatedArticleContent || "");
		setArticleContent(updatedArticleContent)		
		analyzeAndUpdateArticleContent(updatedArticleContent as string);
		setNewLinkHref("");
		setNewLinkPhrase("");
		setAddNewLinkModalActive({ active: false, linkType: "" });

		if (linkWasAdded) {			
			saveContent(updatedArticleContent);
			successAlertRef.current?.show("Link added successfully.");
		} else {			
			errorAlertRef.current?.show("Root domain is not allowed to add in internal/external link.");
		}

		setTimeout(() => {
			successAlertRef.current?.close();
		}, 3000);
		setTimeout(() => {
			errorAlertRef.current?.close();
		}, 3000);
	}


	function postToBlogHandler(articleUID: string, updatePublishedArticle: boolean = false) {
		errorAlertRef.current?.close();
		successAlertRef.current?.close();

		// show processing modal
		setRequestModalActive(true);
		setModalText(updatePublishedArticle ? "Updating the article on your site..." : "Posting article to your site...");

		if (articleUID && selectedIntegration) {
			postArticle.mutate({
				articleUID: articleUID,
				selectedIntegration: selectedIntegration,
				selectedIntegrationUniqueID: selectedIntegrationUniqueID,
				postStatus: publishType,
				selectedCategories: checkedCategories ?? 1,
				updatePublishedArticle: updatePublishedArticle
			}, {
				onSuccess: (response) => {
					successAlertRef.current?.show(updatePublishedArticle ? `Article ${articleUID} has been successfully updated on your site!` : `Article ${articleUID} was posted to your site successfully!`);
					setArticlePosted(true);
					setRequestModalActive(false);
					setArticlePostedTo(response.data.posted_to);
					setArticlePostStatus(response.data.article_post_status);
					setArticlePostedOn(dayjs(response.data.posted_on).format("hh:mma DD MMMM YYYY"));
					setArticlePostLink(response.data.link);
					setTimeout(() => {
						successAlertRef.current?.close();
					}, 5000);
				},
				onError: (error) => {
					if (articlePosted) {
						setDisableUpdatePublish(false)
					}
					setRequestModalActive(false);
					if (error?.err_id === 'ARTICLE_ID_NOT_FOUND') {
						errorAlertRef.current?.show(
							"Something went wrong. The published article could not be found. Please make sure the slug or content hasn't been changed manually."
						);
					} else if (selectedIntegration.includes("wordpress")) {
						errorAlertRef.current?.show(
							"Publishing failed! WordPress rejected the request. A security plugin or Cloudflare might be blocking it. Here are some quick troubleshooting methods: [Fix the issue](https://abun.com/help/why-is-my-abun-article-not-publishing-to-wordpress) or contact support via live chat."
						);
					} else {
						errorAlertRef.current?.show(
							"Publishing failed! Something went wrong. We've logged the error and are looking into it. Please Check your site settings and try again later."
						);
					}
					setTimeout(() => {
						errorAlertRef.current?.close();
					}, 5000);
				}
			})
		} else {
			setRequestModalActive(false);
			errorAlertRef.current?.show("Key information is missing. Please try again.");
			setTimeout(() => {
				errorAlertRef.current?.close();
			}, 5000);
		}
	}

	function scheduleArticleHandler(articleUID: string) {
		if (articleUID && selectedIntegration && selectedIntegrationUniqueID && schedulePublishDateTime) {
			// check if the schedule date is in the future
			const currentDate = new Date();
			try {
				const scheduleDate = schedulePublishDateTime.toDate();
				if (scheduleDate <= currentDate) {
					// close all alerts
					successAlertRef.current?.close();
					errorAlertRef.current?.close();

					errorAlertRef.current?.show("Please select a future date and time for scheduling.");
					setTimeout(() => {
						errorAlertRef.current?.close();
					}, 5000);
					return;
				}
			} catch (error) {
				// close all alerts
				successAlertRef.current?.close();
				errorAlertRef.current?.close();

				console.error(error);
				errorAlertRef.current?.show("Date is not in valid ISO format. Please try again.");
				setTimeout(() => {
					errorAlertRef.current?.close();
				}, 5000);
				return;
			}

			scheduleArticle.mutate({
				articleUID: articleUID,
				articleScheduleDate: schedulePublishDateTime,
				integrationName: selectedIntegration,
				integrationUniqueID: selectedIntegrationUniqueID,
				postStatus: publishType,
			}, {
				onSuccess: (data) => {
					let responseData = (data as any)["data"];

					if (responseData["success"]) {
						// close all alerts
						successAlertRef.current?.close();
						errorAlertRef.current?.close();

						document.querySelector(".publish-dropdown")?.classList.remove("show");
						setArticleScheduledForPosting(true);
						setArticleScheduledDatetime(schedulePublishDateTime);
						successAlertRef.current?.show(articleScheduledForPosting ? "Article rescheduled successfully!" : "Article scheduled successfully!");
						setTimeout(() => {
							successAlertRef.current?.close();
						}, 5000);
					} else {
						// close all alerts
						successAlertRef.current?.close();
						errorAlertRef.current?.close();

						errorAlertRef.current?.show(responseData["message"]);
						setTimeout(() => {
							errorAlertRef.current?.close();
						}, 5000);
					}
				},
				onError: (error) => {
					console.error(error);

					// close all alerts
					successAlertRef.current?.close();
					errorAlertRef.current?.close();

					errorAlertRef.current?.show("Oops! Something went wrong. Please try again.");
					setTimeout(() => {
						errorAlertRef.current?.close();
					}, 5000);
				},
			});
		} else {
			errorAlertRef.current?.show("Key information is missing. Please try again.");
			setTimeout(() => {
				errorAlertRef.current?.close();
			}, 5000);
		}
	}

	function truncateSiteDomain(domain: string) {
		if (domain.includes("webflow")) {
			domain = domain.replace(new RegExp("webflow - ", 'g'), '');
		} else if (domain.includes("wix")) {
			domain = domain.replace(new RegExp("wix - ", 'g'), '');
		} else {
			domain = domain.replace(new RegExp("wordpress - ", 'g'), '');
		}

		if (domain.length <= 20) {
			return domain;
		} else {
			domain = domain.substring(0, 17) + "...";
		}

		return domain;
	}

	function twyaSubmitHandler() {
		// close all alerts
		successAlertRef.current?.close();
		errorAlertRef.current?.close();

		// show a loader modal
		setRequestModalActive(true);
		setModalText("Your command is being processed...");
		if (articleUID) {
			twyaSubmit.mutate({
				usersPrompt: twyaCustomInstructions ?? "",
				articleUID: articleUID
			}, {
				onSuccess: (data) => {
					// hide the loader modal
					setRequestModalActive(false);

					let responseData = (data as any)["data"];
					if (responseData["success"]) {
						successAlertRef.current?.show("Article updated successfully!");
						setTimeout(() => {
							successAlertRef.current?.close();
						}, 5000);
					} else {
						errorAlertRef.current?.show(responseData["message"]);
						setTimeout(() => {
							errorAlertRef.current?.close();
						}, 5000);
					}
					// refresh the article content
					loadArticleContent.refetch();
				},
				onError: (error) => {
					// hide the loader modal
					setRequestModalActive(true);

					console.error(error);
					errorAlertRef.current?.show("Oops! Something went wrong. Please try again.");
					setTimeout(() => {
						errorAlertRef.current?.close();
					}, 5000);
				},
			});
		} else {
			errorAlertRef.current?.show("Key information is missing. Please try again.");
			setTimeout(() => {
				errorAlertRef.current?.close();
			}, 5000);
		}
	}

	if (loadArticleContent.error) {
		return (
			<div className={"card mt-4 article-editor-page-card"}>
				<div className={"card-content"}>
					<div className={"content is-flex is-justify-content-center"}>
						<p>Oops! Something went wrong :(</p>
					</div>
				</div>
			</div>
		)

	} else if (loadArticleContent.isFetching) {
		return (
			<div className={"card mt-4 article-editor-page-card"}>
				<div className={"card-content"}>
					<div className={"content is-flex is-justify-content-center"}>
						<p style={{ textAlign: "center", fontSize: "1.3rem" }}>
							Loading Data...<Icon iconName={"spinner"} marginClass={"ml-5"} />
						</p>
					</div>
				</div>
			</div>
		)

	} else {
		return (
			<>
				{/* ------------------------------ ONGOING REQUEST MODAL ------------------------------ */}
				<AbunModal active={requestModalActive}
					headerText={""}
					closeable={false}
					hideModal={() => {
						setRequestModalActive(false)
					}}>
					<div className={"loadingData w-100 is-flex is-justify-content-center is-align-items-center"}>
						<AbunLoader show={requestModalActive} height="20vh" />
					</div>
					<p className={"is-size-4 has-text-centered mb-4"}>{modalText}</p>
				</AbunModal>

				{/* ------------------------------ EDIT TITLE MODAL ------------------------------ */}
				<AbunModal active={editTitleModalActive}
					headerText={""}
					closeable={true}
					closeableKey={true}
					hideModal={() => {
						setEditTitleModalActive(false);
					}}>
					<label className={"label"}>
						Article Title:
						<input type="text" className={"input mt-2"} value={changeTitleValue} onChange={
							(event) => {
								setChangeTitleValue(event.target.value);
							}
						} />
					</label>
					<div className={"mt-6 is-flex is-justify-content-center"}>
						<GenericButton text={editTitle.isLoading ? "Saving..." : "Save"}
							type={"success"}
							icon={"floppy-disk"}
							disable={editTitle.isLoading}
							additionalClassList={["mr-4"]}
							clickHandler={() => {
								if (articleUID) {
									successAlertRef.current?.close();
									errorAlertRef.current?.close();
									editTitle.mutate({ articleUID: articleUID, title: changeTitleValue }, {
										onSuccess: (data) => {
											setArticleTitle(data['data']['title']);
											setChangeTitleValue(data['data']['title']);
											setFeaturedImageURL(data['data']['featured_image']);
											setarticleURLSlug(data['data']['url_slug'])
											setEditTitleModalActive(false);
											setDisableSave(false)
											successAlertRef.current?.show("Article title for this post has been updated successfully!");
											setTimeout(() => {
												successAlertRef.current?.close();
											}, 5000);
										},
										onError: (error) => {
											console.error(error);
											errorAlertRef.current?.show("Oops! Something went wrong. Please try again.");
											setTimeout(() => {
												errorAlertRef.current?.close();
											}, 5000);
										},
									});
								} else {
									console.error("article title edit failed due to missing article uid");
								}
							}} />

						{/* <GenericButton text={"Regenerate Title"}
						type={"primary"}
						disable={regenerateTitle.isLoading}
						additionalClassList={["mr-4"]}
						clickHandler={() => {
							setShowRegenerateConfirmModal(true);
							setEditTitleModalActive(false);
						}} /> */}
					</div>
				</AbunModal>

				{/* ------------------------------ Upload Featured Image Modal ------------------------------ */}
				<AbunModal active={uploadFeaturedImageModalActive}
					headerText={"Upload Featured Image"}
					closeable={true}
					closeableKey={true}
					hideModal={() => {
						setUploadFeaturedImageModalActive(false);
					}}>
					<UploadFeaturedImage
						articleUid={articleUID || ""}
						featuredImageURL={featuredImageURL || ""}
						setFeaturedImageURL={setFeaturedImageURL}
						hideModal={() => {
							setUploadFeaturedImageModalActive(false);
						}}
						errorAlertRef={errorAlertRef}
						successAlertRef={successAlertRef}
					/>
				</AbunModal>

				{/* ------------------------------ Add New Link Modal ------------------------------ */}
				<AbunModal active={addNewLinkModalActive.active}
					headerText={`Add New ${addNewLinkModalActive.linkType.charAt(0).toUpperCase() + addNewLinkModalActive.linkType.slice(1)} Link`}
					closeable={true}
					closeableKey={true}
					hideModal={() => {
						setAddNewLinkModalActive({ active: false, linkType: "" });
					}}>
					<div className={"mt-4"}>
						<div className="mb-4">
							<p className="mb-3 is-size-6">Enter article phrase*</p>
							<Input value={newLinkPhrase} className="add-link-container" type={"text"} placeholder={`enter the phrase from article that you want to set as ${addNewLinkModalActive.linkType} link`} onChange={(value) => setNewLinkPhrase(value)} />
						</div>
						<div className="mb-4">
							<p className="mb-3 is-size-6">Enter {addNewLinkModalActive.linkType} link URL*</p>
							<Input value={newLinkHref} className="add-link-container" type={"text"} placeholder={`enter ${addNewLinkModalActive.linkType} link URL`} onChange={(value) => setNewLinkHref(value)} />
						</div>
						<AbunButton type={"success"}
							disabled={!newLinkHref || !newLinkPhrase}
							clickHandler={addNewLinkHandler}
						>
							<Icon iconName={"floppy-disk"} additionalClasses={["icon-white", "mr-3"]} />
							Add
						</AbunButton>
					</div>
				</AbunModal>

				{/* show modal to connect internal website */}
				{
					showConnectWebsiteWarningModal &&
					<div className={"modal is-active"}>
						<div className={"modal-background"}></div>
						<div className={"modal-content"}>
							<div className={"modal-card"}>
								<header className={"modal-card-head"}>
									<p className={"modal-card-title"}>Connect Your Website</p>
									<button type="button" className={"delete"} aria-label="close" onClick={() => setShowConnectWebsiteWarningModal(false)}></button>
								</header>
								<section className={"modal-card-body"}>
									<p>
										Please connect your website to get the competition analysis for your website.
									</p>
								</section>
								<footer className={"modal-card-foot is-justify-content-center is-align-items-center"}>
									<AbunButton type={"primary"} clickHandler={() => {
										setShowConnectWebsiteWarningModal(false);
										setShowConnectWebsiteModal(true);
									}}>Connect Website</AbunButton>
								</footer>
							</div>
						</div>
					</div>
				}
				{
					showConnectWebsiteModal && <ConnectWebsite setShowConnectWebsiteModal={setShowConnectWebsiteModal} successAlertRef={successAlertRef} failAlertRef={failAlertRef} />
				}

				<div className={"article-editor-page-container"}>
					<div className={`article-editor-page ${currTab === "twya" ? "twya-tab" : ""}`}>

						{/* ------------------------- Main Content Card ------------------------- */}
						<div className={"card article-editor-page-header w-100"} style={{ flexWrap: articlePosted ? "wrap" : "wrap" }}>
							<div className={"left-header-section"} style={{ flexWrap: articlePosted ? "nowrap" : "wrap", marginLeft: articlePosted ? "2rem" : "8rem" }}>
								<span className={"back-btn"} style={{ cursor: "pointer" }} onClick={goBack}>
									<svg className="back-btn" width="30" height="24" viewBox="0 0 30 24" fill="none" xmlns="http://www.w3.org/2000/svg">
										<path d="M26.0435 12.0003H2.82031M2.82031 12.0003L12.8382 1.98242M2.82031 12.0003L12.8382 22.0181" stroke="black" stroke-opacity="0.5" stroke-width="3" />
									</svg>
								</span>
								<a href="/" onClick={(e) => { e.preventDefault(); navigate("/"); }}>
									<svg className="abun-logo" width="52" height="48" viewBox="0 0 52 48" >
										<rect x="2.125" y="4.41016" width="47.9091" height="42.0909" rx="6.5" fill="black" stroke="black" stroke-width="3" />
										<rect x="0.5" y="0.5" width="49.9091" height="44.0909" rx="7.5" fill="white" stroke="black" />
										<path d="M40 37.3373H29.7561V34.7968C28.2195 36.6746 24.8618 38 21.4472 38C17.3496 38 12 35.2939 12 29.2189C12 22.5917 17.3496 20.714 21.4472 20.714C25.0325 20.714 28.2764 21.8185 29.7561 23.641V20.8797C29.7561 19.002 27.9919 17.5661 24.6341 17.5661C22.0732 17.5661 19.1707 18.5602 17.0081 20.1617L13.5366 14.0316C17.2358 11.1598 22.3577 10 26.5122 10C33.3415 10 40 12.3195 40 21.211V37.3373ZM25.7154 31.5385C27.3089 31.5385 29.0732 31.0414 29.7561 30.1026V28.6114C29.0732 27.6726 27.3089 27.1755 25.7154 27.1755C24.0081 27.1755 22.1301 27.7278 22.1301 29.3846C22.1301 31.0414 24.0081 31.5385 25.7154 31.5385Z" fill="black" />
									</svg>
								</a>
								<div className={"Tabs"}>
									<div className={`Tab article-tab ${currTab === "article" ? "active" : ""}`} onClick={() => {
										setCurrTab("article");
										setIsOptionSidebarCollapsed(false);
										setIsSidebarCollapsed(false);
									}}>
										Article
									</div>
									{
										isProduction ?
											<div className={`Tab twya-tab talk-with-article ${articlePosted ? "article-post" : ""}`}>Talk with your Article (Coming Soon)</div> :
											<div className={`Tab twya-tab talk-with-article" ${currTab === "twya" ? "active" : ""}`} onClick={() => {
												setCurrTab("twya");
												setIsOptionSidebarCollapsed(false);
												setIsSidebarCollapsed(false);
											}}>
												Talk with your Article
											</div>
									}
								</div>
							</div>

							{
								isSmallScreen ?
									<>
										<div className="is-flex w-100">
											<div className={"left-header-section lhs-for-small-screens"}>
												<span className={"back-btn"} style={{ cursor: "pointer" }} onClick={goBack}>
													<svg className="back-btn" width="30" height="24" viewBox="0 0 30 24" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path d="M26.0435 12.0003H2.82031M2.82031 12.0003L12.8382 1.98242M2.82031 12.0003L12.8382 22.0181" stroke="black" stroke-opacity="0.5" stroke-width="3" />
													</svg>
												</span>
											</div>
											<div className={"right-header-section rhs-for-small-screens w-100"}>
												{!articlePosted && !processing &&
													<p className={`rhs-for-small-screens-item ${isOptionSidebarCollapsed ? "" : "active"}`} onClick={() => {
														if (isOptionSidebarCollapsed) {
															setIsOptionSidebarCollapsed(false);
															setIsSidebarCollapsed(true);
														} else {
															setIsOptionSidebarCollapsed(true);
														}
													}}>Options</p>
												}
												<p className={`rhs-for-small-screens-item mr-5 ${isSidebarCollapsed ? "" : "active"}`} onClick={() => {
													if (isSidebarCollapsed) {
														setIsSidebarCollapsed(false);
														setIsOptionSidebarCollapsed(true);
													} else {
														setIsSidebarCollapsed(true);
													}
												}}>Details</p>
											</div>
										</div>
									</> :
									<div className={`right-header-section ${articlePosted ? "article-posted" : ""}`}>
										{/* ------------------------- Copy Text Options ------------------------- */}
										{
											articleContent && (
												<div className={"copy-txt-options"}>
													<div className={"copy-txt-as-btn"} onClick={() => {
														document.querySelector(".copy-txt-dropdown")?.classList.toggle("show");
														// close other dropdowns
														document.querySelector(".publish-dropdown")?.classList.remove("show");
														document.querySelector(".download-dropdown")?.classList.remove("show");
													}}>
														<span style={{ whiteSpace: "nowrap" }}>Copy Text as</span>
														<svg width="5" height="3" viewBox="0 0 5 3" fill="none" xmlns="http://www.w3.org/2000/svg">
															<path d="M0.420536 0H4.57946C4.95342 0 5.1404 0.46513 4.87653 0.736306L2.79707 2.87339C2.63281 3.0422 2.36719 3.0422 2.20468 2.87339L0.123469 0.736306C-0.140396 0.46513 0.0465814 0 0.420536 0Z" fill="black" />
														</svg>
													</div>
													<div className={"copy-txt-dropdown"}>
														<button
															className="copy-txt-item copy-markdown-btn"
															onClick={() => {
																setRequestModalActive(true);
																setModalText("Copying markdown...");

																navigator.clipboard.writeText(aiEditorMarkdownContent as string)
																	.then(() => {
																		setRequestModalActive(false);
																		// show success alert
																		successAlertRef.current?.show("Markdown copied successfully!");
																		// Close the alert after 3 seconds
																		setTimeout(() => {
																			successAlertRef.current?.close();
																		}, 3000);
																		// document.querySelector(".copy-txt-dropdown")?.classList.remove("show");
																	})
																	.catch(() => {
																		setRequestModalActive(false);
																		// show error alert
																		errorAlertRef.current?.show("Failed to copy markdown. Please try again.");
																		// Close the alert after 3 seconds
																		setTimeout(() => {
																			errorAlertRef.current?.close();
																		}, 3000);
																	});
															}}
														>
															Markdown
														</button>
														<button className={"copy-txt-item"} onClick={() => {
															// show processing modal
															setRequestModalActive(true);
															setModalText("Processing HTML file...");
															navigator.clipboard.writeText(aiEditorHtmlContent as string).then(() => {
																setRequestModalActive(false);
																setModalText("");
																// show success alert
																successAlertRef.current?.show("HTML copied successfully.");
																// Close the alert after 3 seconds
																setTimeout(() => {
																	successAlertRef.current?.close();
																}, 3000);
															}).catch(() => {
																setRequestModalActive(false);
																setModalText("");
																// show error alert
																errorAlertRef.current?.show("Failed to copy HTML. Please try again.");
																// Close the alert after 3 seconds
																setTimeout(() => {
																	errorAlertRef.current?.close();
																}, 3000);
															})
															// document.querySelector(".copy-txt-dropdown")?.classList.toggle("show");
														}}>
															<span>HTML</span>
														</button>
														<button className={"copy-txt-item"} onClick={() => {
															// plain text
															navigator.clipboard.writeText(aiEditorTextContent as string).then(() => {
																// show success alert
																successAlertRef.current?.show("Text copied successfully.");
																// Close the alert after 3 seconds
																setTimeout(() => {
																	successAlertRef.current?.close();
																}, 3000);
															}).catch(() => {
																// show error alert
																errorAlertRef.current?.show("Failed to copy text. Please try again.");
																// Close the alert after 3 seconds
																setTimeout(() => {
																	errorAlertRef.current?.close();
																}, 3000);
															})
															// document.querySelector(".copy-txt-dropdown")?.classList.toggle("show");
														}}>
															<span>Raw Text</span>
														</button>
													</div>
												</div>
											)
										}

										{/* ------------------------- Download Options ------------------------- */}
										{
											articleContent && (
												<div className={"download-options"}>
													<div className={"download-btn"} onClick={() => {
														document.querySelector(".download-dropdown")?.classList.toggle("show");
														// close other dropdowns
														document.querySelector(".publish-dropdown")?.classList.remove("show");
														document.querySelector(".copy-txt-dropdown")?.classList.remove("show");
													}}>
														<span>Download</span>
														<svg width="5" height="3" viewBox="0 0 5 3" fill="none" xmlns="http://www.w3.org/2000/svg">
															<path d="M0.420536 0H4.57946C4.95342 0 5.1404 0.46513 4.87653 0.736306L2.79707 2.87339C2.63281 3.0422 2.36719 3.0422 2.20468 2.87339L0.123469 0.736306C-0.140396 0.46513 0.0465814 0 0.420536 0Z" fill="black" />
														</svg>
													</div>
													<div className={"download-dropdown"}>
														<button className={"download-item"} onClick={() => {
															// show processing modal
															setRequestModalActive(true);
															setModalText("Processing markdown file...");
															// create a blob with the markdown content
															const markdownBlob = new Blob([aiEditorMarkdownContent as string], { type: 'text/markdown' });
															// create a download link
															const articleMarkdownDownload = document.createElement('a');
															// set the download link href to the blob URL
															articleMarkdownDownload.href = URL.createObjectURL(markdownBlob);
															// set the download link download attribute to the filename
															articleMarkdownDownload.download = `Abun-${articleTitle}-articleUID.md`;
															// append the download link to the body
															document.body.appendChild(articleMarkdownDownload);
															// click the download link
															articleMarkdownDownload.click();
															// remove the download link from the body
															document.body.removeChild(articleMarkdownDownload);
															setRequestModalActive(false);
															setModalText("");
															document.querySelector(".download-dropdown")?.classList.toggle("show");
														}}>
															<span>Markdown</span>
														</button>
														<button className={"download-item"} onClick={() => {
															// show processing modal
															setRequestModalActive(true);
															setModalText("Processing HTML file...");
															// create a blob with the html content
															const htmlBlob = new Blob([aiEditorHtmlContent as string], { type: 'text/html' });
															// create a download link
															const articleHtmlDownload = document.createElement('a');
															// set the download link href to the blob URL
															articleHtmlDownload.href = URL.createObjectURL(htmlBlob);
															// set the download link download attribute to the filename
															articleHtmlDownload.download = `Abun-${articleTitle}-articleUID.html`;
															// append the download link to the body
															document.body.appendChild(articleHtmlDownload);
															// click the download link
															articleHtmlDownload.click();
															// remove the download link from the body
															document.body.removeChild(articleHtmlDownload);
															setRequestModalActive(false);
															setModalText("");
															document.querySelector(".download-dropdown")?.classList.toggle("show");
														}}>
															<span>HTML</span>
														</button>
													</div>
												</div>
											)
										}

										{/* ------------------------- Publish Options ------------------------- */}
										{
											!articlePosted &&
											<div className={"publish-options"}>
												<div className={"publish-btn"} onClick={() => {		
													document.querySelector(".publish-dropdown")?.classList.toggle("show");
													// close other dropdowns
													document.querySelector(".copy-txt-dropdown")?.classList.remove("show");
													document.querySelector(".download-dropdown")?.classList.remove("show");
												}}>
													<span>Publish / Schedule</span>
													<svg width="5" height="3" viewBox="0 0 5 3" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path d="M0.420536 0H4.57946C4.95342 0 5.1404 0.46513 4.87653 0.736306L2.79707 2.87339C2.63281 3.0422 2.36719 3.0422 2.20468 2.87339L0.123469 0.736306C-0.140396 0.46513 0.0465814 0 0.420536 0Z" fill="black" />
													</svg>
												</div>
												<div className="publish-dropdown">
													<div className="card-header">
														Ready to publish your post?
													</div>
													{
														integrationWithUniqueID.length > 0 ?
															<>
																<div className="card-content space-y-4">
																	<FormControl fullWidth>
																		<InputLabel id="platform-select-label">{selectedIntegration?.split(" - ")[0] || 'Platform'}</InputLabel>
																		<Select
																			labelId="platform-select-label"
																			id="platform-select-label"
																			value={selectedIntegrationUniqueID}
																			label={selectedIntegration?.split(" - ")[0] || 'Platform'}																																							
																			onChange={(e) => setSelectedIntegrationUniqueID(e.target.value)}
																			MenuProps={{
																				disablePortal: true,
																				PaperProps: {
																				onMouseDown: (e) => e.stopPropagation(),
																				},
																			}}
																		>
																			{integrationWithUniqueID.map((integration, index) => (
																				<MenuItem key={integration.integrationUniqueID} value={integration.integrationUniqueID}
																					onClick={() => {
																						handleIntegartionChange(integration.integrationUniqueID, integration.integrationName)																						
																					}}
																				>
																					{integration.integrationName?.split(" - ")[1]}
																				</MenuItem>
																			))}
																		</Select>
																	</FormControl>

																	<FormControl fullWidth>
																		<InputLabel id="type-label">Type</InputLabel>
																		<Select
																			labelId="type-label"
																			id="type"
																			defaultValue={publishType}
																			label="Type"
																			onChange={(e) => setPublishType(e.target.value)}
																			MenuProps={{
																				disablePortal: true,
																				PaperProps: {
																				onMouseDown: (e) => e.stopPropagation(),
																				},
																			}}
																		>
																			<MenuItem value="publish">Published Post</MenuItem>
																			<MenuItem value="draft">Draft</MenuItem>
																		</Select>
																	</FormControl>

																	<div className="radio-group" aria-label="publish options">
																		<label className="form-control-label text-sm">
																			<input
																				type="radio"
																				name="publishOption"
																				value="publish"
																				checked={publishOption === 'publish'}
																				onChange={(e) => setPublishOption(e.target.value)}
																			/>
																			Publish it live
																		</label>
																		<p className="text-sm text-muted-foreground">
																			Publish this post immediately
																		</p>

																		<label className="form-control-label text-sm">
																			<input
																				type="radio"
																				name="publishOption"
																				value="schedule"
																				checked={publishOption === 'schedule'}
																				onChange={(e) => setPublishOption(e.target.value)}
																			/>
																			Schedule it for later
																		</label>
																		<p className="text-sm text-muted-foreground">
																			Set automatic future publish date
																		</p>
																	</div>

																	{publishOption === 'schedule' && (
																		<div className="grid grid-cols-2 gap-4">
																			<div className="form-group">
																				<label htmlFor="publishDate">Date</label>
																				<input
																					type="date"
																					id="publishDate"
																					className="form-control"
																					defaultValue={articleScheduledForPosting ? articleScheduledDatetime?.format("YYYY-MM-DD") : schedulePublishDateTime?.format("YYYY-MM-DD")}
																					onChange={(e) => {
																						setSchedulePublishDateTime(dayjs(e.target.value + " " + schedulePublishTime))
																						setSchedulePublishDate(e.target.value);
																					}}
																				/>
																			</div>
																			<div className="form-group">
																				<label htmlFor="publishTime">Time</label>
																				<input
																					type="time"
																					id="publishTime"
																					className="form-control"
																					defaultValue={articleScheduledForPosting ? articleScheduledDatetime?.format("HH:mm") : schedulePublishDateTime?.format("HH:mm")}
																					onChange={(e) => {
																						setSchedulePublishDateTime(dayjs(schedulePublishDate + " " + e.target.value))
																						setSchedulePublishTime(e.target.value);
																					}}
																				/>
																			</div>
																		</div>
																	)}																	

																</div>

																<footer className="p-4">
																	<button className="btn btn-primary ml-auto"
																		disabled={processing || articlePosted}
																		onClick={() => {
																			if (articleUID) {
																				publishOption === 'publish' ? postToBlogHandler(articleUID) : scheduleArticleHandler(articleUID);
																			} else {
																				console.error("Post to Blog failed. Article UID missing.")
																			}
																		}}
																	>
																		{publishOption === 'publish' ? 'Publish' : articleScheduledForPosting ? 'Reschedule' : 'Schedule'}
																	</button>
																</footer>
															</>
															:
															<div className="card-content space-y-4">
																<p className="text-sm text-muted-foreground">
																	Please integrate your blog to publish your article.
																</p>
																<div className="mb-2">
																	<NavLink to={pageURL['new-integration']} className="text-blue-600 hover:text-blue-800 underline">
																		Go to Integrations
																	</NavLink>
																</div>
															</div>
													}
												</div>
											</div>
										}

										<div className="save-collapse">
											{articlePosted ? (
												<>
													<AbunButton
														type={"primary"}
														clickHandler={handleSaveClick}
														disabled={disableSave}>
														{saveButtonText}
													</AbunButton>
													<AbunButton
														type={"primary"}
														clickHandler={handleUpdateArticle}
														disabled={disableUpdatePublish}>
														Update Publish Article
													</AbunButton>
													<AbunButton
														type={"primary"}
														clickHandler={() => {
															window.open(articlePostLink, '_blank');
														}}>
														{articlePostStatus === "publish" ? "View Published Article" : " View Draft Article"}
														<img src={newTab} alt="new-tab" style={{ height: 'auto', width: '1.4em', filter: 'invert(1)', marginLeft: '0.5em' }} />
													</AbunButton>
												</>
											) : (
												<AbunButton
													type={"primary"}
													clickHandler={handleSaveClick}
													disabled={disableSave}>
													{saveButtonText}
												</AbunButton>
											)}
											<svg className={`collapse-button ${isSidebarCollapsed ? "" : "collapsed"}`}
												onClick={() => {
													if (isSidebarCollapsed) {
														setIsSidebarCollapsed(false);
													} else {
														setIsSidebarCollapsed(true);
													}
												}} width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
												<path fill-rule="evenodd" clip-rule="evenodd" d="M14 0H2C0.9 0 0 0.9 0 2V14C0 15.1 0.9 16 2 16H14C15.1 16 16 15.1 16 14V2C16 0.9 15.1 0 14 0ZM10 14.5H2C1.7 14.5 1.5 14.3 1.5 14V2C1.5 1.7 1.7 1.5 2 1.5H10V14.5ZM14.5 14C14.5 14.3 14.3 14.5 14 14.5H11.5V1.5H14C14.3 1.5 14.5 1.7 14.5 2V14Z" fill="black" />
											</svg>
										</div>
									</div>
							}
						</div>

						<div className="article-editor-page-body">
							{
								(!processing) ?
									<div className={`article-editor-section ${isSidebarCollapsed ? "collapsed" : ""}`} style={isMediumScreen ? isSidebarCollapsed ? { display: "block" } : { display: "none" } : { display: "block" }}>
										{/* ------------------ Article Feedback ------------------ */}
										<div className={"article-feedback-container"}>
											<svg width="20" height="20" viewBox="0 0 25 25" fill="none" className={`positive-feedback ${articleFeedback === "positive" ? "active" : ""}`} onClick={() => {
												if (articleFeedback !== "positive") {
													setArticleFeedback("positive");
													saveContent(undefined, undefined, "positive");
												}
											}}>
												<path fill-rule="evenodd" clip-rule="evenodd" d="M3.75022 25C1.67903 25 0 23.3211 0 21.25V13.75C0 11.6789 1.67903 10 3.75022 10H5.47657L9.44153 2.07295C10.0394 0.877241 11.2249 0.0960319 12.5465 0.0082624L12.7958 0H13.1258C14.7827 0 16.1385 1.28942 16.2443 2.91953L16.2509 3.125V10H21.2491C21.496 10 21.7424 10.0244 21.9845 10.0728C23.943 10.4645 25.2377 12.3155 24.9634 14.2681L24.9265 14.4854L23.4264 21.9854C23.0904 23.6652 21.6628 24.894 19.9711 24.9935L19.749 25H3.75022ZM5.00029 12.5H3.75022C3.05982 12.5 2.50015 13.0596 2.50015 13.75V21.25C2.50015 21.9404 3.05982 22.5 3.75022 22.5H5.00029V12.5ZM13.1258 2.5H12.7958C12.3749 2.5 11.9862 2.71136 11.7563 3.05575L11.6777 3.19098L7.63241 11.2812C7.56732 11.4113 7.52563 11.5515 7.50885 11.6954L7.50044 11.8402V22.5H19.749C20.2952 22.5 20.7718 22.1466 20.9375 21.6377L20.9748 21.4951L22.4749 13.9951C22.6103 13.3182 22.1712 12.6597 21.4942 12.5243L21.3722 12.5061L21.2491 12.5H16.2509C14.9329 12.5 13.8531 11.4802 13.7577 10.1866L13.7508 10V3.125C13.7508 2.81818 13.5297 2.56299 13.2381 2.51007L13.1258 2.5Z" fill="#6C6C6C" fill-opacity="1" />
											</svg>
											<svg width="20" height="20" viewBox="0 0 25 25" fill="none" className={`negative-feedback ${articleFeedback === "negative" ? "active" : ""}`} onClick={() => {
												if (articleFeedback !== "negative") {
													setArticleFeedback("negative");
													saveContent(undefined, undefined, "negative");
												}
											}}>
												<path fill-rule="evenodd" clip-rule="evenodd" d="M3.75022 25C1.67903 25 0 23.3211 0 21.25V13.75C0 11.6789 1.67903 10 3.75022 10H5.47657L9.44153 2.07295C10.0394 0.877241 11.2249 0.0960319 12.5465 0.0082624L12.7958 0H13.1258C14.7827 0 16.1385 1.28942 16.2443 2.91953L16.2509 3.125V10H21.2491C21.496 10 21.7424 10.0244 21.9845 10.0728C23.943 10.4645 25.2377 12.3155 24.9634 14.2681L24.9265 14.4854L23.4264 21.9854C23.0904 23.6652 21.6628 24.894 19.9711 24.9935L19.749 25H3.75022ZM5.00029 12.5H3.75022C3.05982 12.5 2.50015 13.0596 2.50015 13.75V21.25C2.50015 21.9404 3.05982 22.5 3.75022 22.5H5.00029V12.5ZM13.1258 2.5H12.7958C12.3749 2.5 11.9862 2.71136 11.7563 3.05575L11.6777 3.19098L7.63241 11.2812C7.56732 11.4113 7.52563 11.5515 7.50885 11.6954L7.50044 11.8402V22.5H19.749C20.2952 22.5 20.7718 22.1466 20.9375 21.6377L20.9748 21.4951L22.4749 13.9951C22.6103 13.3182 22.1712 12.6597 21.4942 12.5243L21.3722 12.5061L21.2491 12.5H16.2509C14.9329 12.5 13.8531 11.4802 13.7577 10.1866L13.7508 10V3.125C13.7508 2.81818 13.5297 2.56299 13.2381 2.51007L13.1258 2.5Z" fill="#6C6C6C" fill-opacity="1" />
											</svg>
										</div>
										<div className={`article-editor ${articlePosted ? "article-posted-editor" : ""}`}>
											<div className="is-flex" style={{ width: '100%' }}>
												{isEditingTitle ? (
													<textarea
														className="input article-title editable-input"
														value={changeTitleValue}
														autoFocus
														rows={1}
														onChange={(e) => {
															setChangeTitleValue(e.target.value);

															// Auto-grow height based on content
															const textarea = e.target;
															textarea.style.height = "auto";
															textarea.style.height = textarea.scrollHeight + "px";
														}}
														onBlur={() => {
															setIsEditingTitle(false);
															if (changeTitleValue.trim() !== "" && changeTitleValue !== articleTitle) {
																editTitle.mutate({ articleUID: articleUID, title: changeTitleValue }, {
																	onSuccess: (data) => {
																		setArticleTitle(data['data']['title']);
																		setChangeTitleValue(data['data']['title']);
																		successAlertRef.current?.show("Article title for this post has been updated successfully!");
																		setTimeout(() => {
																			successAlertRef.current?.close();
																		}, 5000);
																	},
																	onError: (error) => {
																		console.error(error);
																		setChangeTitleValue(articleTitle);
																		errorAlertRef.current?.show("Oops! Something went wrong. Please try again.");
																		setTimeout(() => {
																			errorAlertRef.current?.close();
																		}, 5000);
																	}
																});
															} else {
																setChangeTitleValue(articleTitle);
															}
														}}
														onKeyDown={(e) => {
															if (e.key === "Enter") {
																e.preventDefault();
																(e.target as HTMLTextAreaElement).blur();
															}
														}}
													/>

												) : (
													<h1 className="article-title" onClick={() => setIsEditingTitle(true)}>
														{articleTitle}
													</h1>
												)}
											</div>


											<div ref={articleEditorDivRef} onBlur={handleContentChange} onInput={handleContentChange} />
										</div>
									</div> :
									<div className={"article-generating"}>
										<Player
											autoplay
											loop
											src="https://lottie.host/91a433df-05fa-4ab3-94b2-2c2a0a16a67f/2SoIqH8Kh3.json"
											style={{ height: '300px', width: '300px' }}
										>
										</Player>
										<h2 className={"is-size-3 font-secondary has-text-weight-bold has-text-primary"}>
											An Amazing SEO Article is being cooked for your site!
										</h2>
										<p className={"is-size-5 mt-4"}>Article generation should take around 3-5 minutes</p>
										<ul className={"mt-4 mb-4"} style={{ listStyleType: "none" }}>
											<li className={"mb-2"}><Icon iconName={"green-checkmark-circle"} />&nbsp;&nbsp;SEO Optimized Article
											</li>
											<li className={"mb-2"}><Icon iconName={"green-checkmark-circle"} />&nbsp;&nbsp;With Image & Alt Text
											</li>
											<li className={"mb-2"}><Icon iconName={"green-checkmark-circle"} />&nbsp;&nbsp;With Internal Links</li>
											<li className={"mb-2"}><Icon iconName={"green-checkmark-circle"} />&nbsp;&nbsp;With External Links</li>
											<li className={"mb-2"}><Icon iconName={"green-checkmark-circle"} />&nbsp;&nbsp;With Featured Image</li>
										</ul>
									</div>
							}

							{
								isSidebarCollapsed === false && currTab === "article" &&
								<div className="article-editor-sidebar-container" style={isMediumScreen ? isSidebarCollapsed ? { width: "20%" } : { width: "100%" } : { width: "20%" }}>
									<div className="sidebar-items">
										<div className="sidebar-items-image-remove">
											<h6 className="featured-image">Featured Image</h6>
											{featuredImageURL &&
												<p className="remove-featured-image" onClick={() => {
													removeFeaturedImage.mutate({ articleUID: articleUID }, {
														onSuccess: () => {
															successAlertRef.current?.show("Featured image removed successfully!");
															setFeaturedImageURL("");
															setTimeout(() => {
																successAlertRef.current?.close();
															}, 5000);
														},
														onError: () => {
															errorAlertRef.current?.show("Oops! Failed to remove featured image. Please try again later or contact us for further support.");
															setTimeout(() => {
																errorAlertRef.current?.close();
															}, 5000);
														}
													});
												}}>
													Remove
													<svg width="6" height="6" viewBox="0 0 6 6" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path d="M0.375 5.4375C0.375 5.58668 0.434263 5.72976 0.539752 5.83525C0.645241 5.94074 0.788315 6 0.9375 6H4.3125C4.46168 6 4.60476 5.94074 4.71025 5.83525C4.81573 5.72976 4.875 5.58668 4.875 5.4375V1.5H0.375V5.4375ZM3.5625 2.4375C3.5625 2.38777 3.58225 2.34008 3.61742 2.30492C3.65258 2.26976 3.70027 2.25 3.75 2.25C3.79973 2.25 3.84742 2.26976 3.88258 2.30492C3.91774 2.34008 3.9375 2.38777 3.9375 2.4375V5.0625C3.9375 5.11223 3.91774 5.15992 3.88258 5.19508C3.84742 5.23025 3.79973 5.25 3.75 5.25C3.70027 5.25 3.65258 5.23025 3.61742 5.19508C3.58225 5.15992 3.5625 5.11223 3.5625 5.0625V2.4375ZM2.4375 2.4375C2.4375 2.38777 2.45725 2.34008 2.49242 2.30492C2.52758 2.26976 2.57527 2.25 2.625 2.25C2.67473 2.25 2.72242 2.26976 2.75758 2.30492C2.79274 2.34008 2.8125 2.38777 2.8125 2.4375V5.0625C2.8125 5.11223 2.79274 5.15992 2.75758 5.19508C2.72242 5.23025 2.67473 5.25 2.625 5.25C2.57527 5.25 2.52758 5.23025 2.49242 5.19508C2.45725 5.15992 2.4375 5.11223 2.4375 5.0625V2.4375ZM1.3125 2.4375C1.3125 2.38777 1.33225 2.34008 1.36742 2.30492C1.40258 2.26976 1.45027 2.25 1.5 2.25C1.54973 2.25 1.59742 2.26976 1.63258 2.30492C1.66774 2.34008 1.6875 2.38777 1.6875 2.4375V5.0625C1.6875 5.11223 1.66774 5.15992 1.63258 5.19508C1.59742 5.23025 1.54973 5.25 1.5 5.25C1.45027 5.25 1.40258 5.23025 1.36742 5.19508C1.33225 5.15992 1.3125 5.11223 1.3125 5.0625V2.4375ZM5.0625 0.375002H3.65625L3.54609 0.155861C3.52276 0.109011 3.48681 0.0696024 3.4423 0.0420675C3.39779 0.0145326 3.34648 -3.5484e-05 3.29414 2.05461e-06H1.95469C1.90247 -0.000198694 1.85124 0.0143151 1.80689 0.0418806C1.76254 0.0694461 1.72685 0.108948 1.70391 0.155861L1.59375 0.375002H0.1875C0.137772 0.375002 0.0900805 0.394756 0.0549175 0.429919C0.0197544 0.465082 0 0.512774 0 0.562502L0 0.937502C0 0.98723 0.0197544 1.03492 0.0549175 1.07008C0.0900805 1.10525 0.137772 1.125 0.1875 1.125H5.0625C5.11223 1.125 5.15992 1.10525 5.19508 1.07008C5.23024 1.03492 5.25 0.98723 5.25 0.937502V0.562502C5.25 0.512774 5.23024 0.465082 5.19508 0.429919C5.15992 0.394756 5.11223 0.375002 5.0625 0.375002Z" fill="black" fill-opacity="0.5" />
													</svg>
												</p>
											}
										</div>


										{isFeatureImageLoading ?
											<div className={"loadingData w-100  is-flex-direction-column is-flex is-justify-content-center is-align-items-center"}>
												<AbunLoader show={isFeatureImageLoading} height="17vh" />
												<p className={"is-size-5 has-text-centered mt-1 "}>{modalText}</p>
											</div>
											:
											<img src={featuredImageURL || defaultFeaturedImageURL}
												alt="Featured Image"
												width={"100%"}
												height={"auto"} />
										}

										{
											(articleMetaDescription && !processing) &&
											<div className="FeaturedImageSection-Buttons">
												<div className="GenerateNew"
													onClick={() => {
														if (articleUID) {
															setIsFeatureImageLoading(true);
															setModalText("Generating new featured image...");
															generateNewFeaturedImage.mutate({ articleUID: articleUID }, {
																onSuccess: (response) => {
																	if (response.data && response.data.featured_image) {
																		setFeaturedImageURL(response.data.featured_image);
																	}
																	setIsFeatureImageLoading(false);
																	successAlertRef.current?.show("Featured image generated successfully!");
																	setTimeout(() => {
																		successAlertRef.current?.close();
																	}, 5000);
																},
																onError: () => {
																	setIsFeatureImageLoading(false);
																	errorAlertRef.current?.show("Failed to generate new featured image. Please try again later or contact us for further support.");
																	setTimeout(() => {
																		errorAlertRef.current?.close();
																	}, 5000);
																}
															});
														}
													}
													}>
													<svg width="11" height="11" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path opacity="0.15" d="M5.125 1C5.125 3.27817 6.97181 5.125 9.25 5.125C6.97181 5.125 5.125 6.97181 5.125 9.25C5.125 6.97181 3.27817 5.125 1 5.125C3.27817 5.125 5.125 3.27817 5.125 1Z" fill="black" />
														<path d="M5.125 1C5.125 3.27817 6.97181 5.125 9.25 5.125C6.97181 5.125 5.125 6.97181 5.125 9.25C5.125 6.97181 3.27817 5.125 1 5.125C3.27817 5.125 5.125 3.27817 5.125 1Z" stroke="black" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round" />
													</svg>
													Generate New
												</div>
												<div className="manual-upload-btn"
													onClick={() => {
														setUploadFeaturedImageModalActive(true);
													}
													}>
													Upload
												</div>
												{/* <svg className="settings" width="9" height="9" viewBox="0 0 9 9" fill="none" xmlns="http://www.w3.org/2000/svg">
													<path fill-rule="evenodd" clip-rule="evenodd" d="M5.39271 0.243724C5.43199 0.319918 5.44176 0.417496 5.46124 0.612648C5.49819 0.982035 5.51669 1.16673 5.59399 1.26807C5.69205 1.39661 5.85153 1.46267 6.01178 1.44112C6.13809 1.42412 6.28173 1.30658 6.56906 1.07151C6.72084 0.947318 6.79671 0.885222 6.87838 0.859131C6.98211 0.825997 7.09434 0.831591 7.19424 0.874885C7.2729 0.908973 7.34224 0.978309 7.48093 1.11699L7.88301 1.51905C8.02165 1.65773 8.091 1.72707 8.12511 1.80574C8.1684 1.90565 8.17398 2.01787 8.14086 2.12159C8.11476 2.20325 8.05266 2.27915 7.92846 2.43094C7.69338 2.71827 7.57584 2.86193 7.55888 2.98825C7.53732 3.14848 7.60338 3.30796 7.7319 3.40602C7.83324 3.48332 8.01792 3.50179 8.38733 3.53873C8.58249 3.55825 8.68009 3.56801 8.75628 3.60729C8.85303 3.65724 8.92845 3.74054 8.96845 3.84178C9 3.92153 9 4.01958 9 4.21569V4.78435C9 4.98042 9 5.07847 8.9685 5.15817C8.92845 5.25946 8.85303 5.3428 8.75623 5.39271C8.68005 5.43199 8.58249 5.44176 8.38737 5.46124C8.0181 5.49819 7.83347 5.51664 7.73213 5.5939C7.60352 5.692 7.53745 5.85153 7.55905 6.01182C7.57606 6.13809 7.69356 6.28169 7.92855 6.56892C8.0527 6.72066 8.11476 6.79653 8.14086 6.87816C8.17402 6.98193 8.1684 7.09416 8.12511 7.19411C8.091 7.27277 8.0217 7.34206 7.88306 7.48071L7.48093 7.88283C7.34229 8.02147 7.27295 8.09082 7.19429 8.12493C7.09434 8.16822 6.98215 8.1738 6.87843 8.14068C6.79675 8.11458 6.72084 8.05248 6.56906 7.92828C6.28173 7.6932 6.13809 7.57566 6.01178 7.55869C5.85153 7.53714 5.69205 7.6032 5.59399 7.73172C5.51669 7.83306 5.49823 8.01779 5.46129 8.38723C5.44176 8.58249 5.43199 8.68009 5.39266 8.75632C5.34276 8.85308 5.25946 8.9284 5.15826 8.96845C5.07852 9 4.98042 9 4.78422 9H4.21569C4.01958 9 3.92153 9 3.84178 8.96845C3.74054 8.92845 3.65724 8.85303 3.60729 8.75628C3.56801 8.68009 3.55825 8.58249 3.53873 8.38733C3.50179 8.01792 3.48332 7.83324 3.40601 7.7319C3.30795 7.60338 3.14848 7.53732 2.98826 7.55888C2.86193 7.57584 2.71827 7.69338 2.43094 7.9285C2.27914 8.0527 2.20323 8.1148 2.12156 8.14086C2.01784 8.17402 1.90563 8.1684 1.80572 8.12511C1.72705 8.09104 1.65771 8.0217 1.51902 7.88301L1.11698 7.48093C0.978291 7.34229 0.908946 7.27295 0.874863 7.19429C0.831573 7.09434 0.825975 6.98215 0.859109 6.87843C0.8852 6.79675 0.9473 6.72084 1.0715 6.56906C1.30658 6.28173 1.42412 6.13809 1.44111 6.01173C1.46267 5.85153 1.39661 5.69205 1.26806 5.59399C1.16673 5.51669 0.982035 5.49819 0.612643 5.46124C0.417492 5.44176 0.319918 5.43199 0.243724 5.39271C0.146952 5.34276 0.071559 5.25946 0.0315225 5.15822C0 5.07847 0 4.98042 0 4.78431V4.21573C0 4.01958 2.01166e-08 3.92148 0.031536 3.84174C0.0715725 3.74053 0.146943 3.65724 0.243684 3.60734C0.319896 3.56801 0.417497 3.55825 0.612698 3.53873C0.982184 3.50178 1.16694 3.48331 1.26829 3.40596C1.39678 3.30791 1.46281 3.14849 1.44129 2.9883C1.42431 2.86194 1.30673 2.71823 1.07158 2.43082C0.947322 2.27896 0.8852 2.20302 0.859113 2.12132C0.826007 2.01764 0.8316 1.90547 0.874858 1.80559C0.908946 1.72689 0.978313 1.65753 1.11705 1.51879L1.51904 1.1168C1.65773 0.978106 1.72708 0.908762 1.80575 0.874679C1.90565 0.831389 2.01786 0.825795 2.12158 0.858924C2.20325 0.885015 2.27916 0.947124 2.43099 1.07134C2.71827 1.3064 2.86192 1.42392 2.9882 1.44093C3.14847 1.46251 3.30801 1.39644 3.40608 1.26784C3.48335 1.16652 3.50181 0.981878 3.53874 0.612589C3.55825 0.417492 3.568 0.319941 3.60729 0.243765C3.65719 0.146961 3.74054 0.071541 3.84183 0.0315045C3.92153 -2.34693e-08 4.01958 0 4.21564 0H4.78431C4.98042 0 5.07847 0 5.15822 0.0315225C5.25946 0.071559 5.34276 0.146952 5.39271 0.243724ZM4.5 6.3C5.49409 6.3 6.3 5.49409 6.3 4.5C6.3 3.50589 5.49409 2.7 4.5 2.7C3.50589 2.7 2.7 3.50589 2.7 4.5C2.7 5.49409 3.50589 6.3 4.5 6.3Z" fill="#222222" fill-opacity="0.5" />
												</svg> */}
											</div>
										}
									</div>

									<div className="sidebar-items">
										<h6 onClick={() => {
											const statsContainer = document.querySelector(".sidebar-item-contents.stats-container");
											const statsArrow = document.querySelector(".stats-arrow");
											if (statsContainer && statsArrow) {
												statsContainer.classList.toggle("expanded");
												statsArrow?.classList.toggle("active");
											}
										}}>
											<svg className="stats-arrow" width="11" height="6" viewBox="0 0 11 6" fill="none" xmlns="http://www.w3.org/2000/svg">
												<path d="M0 4.4L5.5 0L11 4.4L10.1 5.6L5.5 2L1 5.6L0 4.4Z" fill="black" fill-opacity="0.5" />
											</svg>
											Stats & Settings
										</h6>
										<div className="sidebar-item-contents stats-container expanded">
											<div className="stats">
												<span>Keyword: </span><span className="stat">{keyword}</span>
											</div>
											<div className="stats">
												<span>Keyword Volume: </span>
												<span className="stat">{keywordTraffic}
													<img
														loading="lazy"
														width="20"
														srcSet={locationIsoCode !== "zz" ? `https://flagcdn.com/32x24/${locationIsoCode.toLowerCase()}.png 2x` : "https://img.icons8.com/?size=100&id=3685"}
														src={locationIsoCode !== "zz" ? `https://flagcdn.com/16x12/${locationIsoCode.toLowerCase()}.png` : "https://img.icons8.com/?size=100&id=3685"}
														alt={locationIsoCode}
													/>
												</span>
											</div>
											<div className="stats">
												<span>Total Words: </span><span className="stat">{wordCount}</span>
											</div>
											<div className="stats">
												<span>Status: </span><span className="stat">{articlePosted ? "Published" : "Not Published"}</span>
											</div>
											<div className="stats active-integration">
												<span >Active Integration: </span>													
													<span className="truncate-text" onClick={() => openInNewTab(selectedIntegration?.split(" - ")[1])}>
														{selectedIntegration?.split(" - ")[1]}
													</span>
													<svg width="24" height="12" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M432 320H400a16 16 0 0 0 -16 16V448H64V128H208a16 16 0 0 0 16-16V80a16 16 0 0 0 -16-16H48A48 48 0 0 0 0 112V464a48 48 0 0 0 48 48H400a48 48 0 0 0 48-48V336A16 16 0 0 0 432 320zM488 0h-128c-21.4 0-32.1 25.9-17 41l35.7 35.7L135 320.4a24 24 0 0 0 0 34L157.7 377a24 24 0 0 0 34 0L435.3 133.3 471 169c15 15 41 4.5 41-17V24A24 24 0 0 0 488 0z" /></svg>													
											
											</div>
											{articlePosted &&
												<>
													<div className="stats">
														<span>Platform: </span><span className="stat">{articlePostedTo.charAt(0).toUpperCase() + articlePostedTo.slice(1)}</span>
													</div>
													{
														(articlePostedTo !== "webflow" || articlePostStatus === "publish") &&
														<div className="stats">
															<span>Live Link: </span><span className="stat"><a href={articlePostLink} target="_blank" rel="noreferrer">{articlePostLink.slice(0, 20)}...</a></span>
														</div>
													}
													{articlePostedOn &&
														<div className="stats">
															<span>Publish Date: </span><span className="stat">{articlePostedOn}</span>
														</div>
													}
												</>
											}
										</div>
									</div>

									<div className="sidebar-items">
										<h6 onClick={() => {
											const internalLinks = document.querySelector(".sidebar-item-contents.internal-links");
											const internalLinksArrow = document.querySelector(".internal-links-arrow");
											if (internalLinks) {
												internalLinks.classList.toggle("expanded");
												internalLinksArrow?.classList.toggle("active");
											}
										}}>
											<svg className="internal-links-arrow active" width="11" height="6" viewBox="0 0 11 6" fill="none" xmlns="http://www.w3.org/2000/svg">
												<path d="M0 4.4L5.5 0L11 4.4L10.1 5.6L5.5 2L1 5.6L0 4.4Z" fill="black" fill-opacity="0.5" />
											</svg>
											Internal Links
										</h6>
										<div className="sidebar-item-contents internal-links">
											{articleInternalLinks &&
												articleInternalLinks.map((link, index) => {
													return (
														<div className="backlinks" key={index}>
															<div className="backlink">
																<span>{link.linkTxt}</span>
																<div className="backlink-url">
																	{
																		articleInternalLinkEditableIndex === index ?
																			<input className="link-edit-input"
																				type="text"
																				value={link.linkHref}
																				onChange={(e) => {
																					saveEditedLinkHandler("internal", link, e.target.value);
																				}}
																				onBlur={() => setArticleInternalLinkEditableIndex(-1)}
																				onKeyDown={(e) => {
																					if (e.key === "Enter") {
																						setArticleInternalLinkEditableIndex(-1);
																					}
																				}}
																			/> :
																			<>
																				<a className={`link ${articlePosted ? "posted" : ""}`} href={link.linkHref} target="_blank" rel="noreferrer">{link.linkHref}</a>

																				<svg className="edit-link" onClick={() => setArticleInternalLinkEditableIndex(index)} width="8" height="8" viewBox="0 0 8 8" fill="none" xmlns="http://www.w3.org/2000/svg">
																					<path fill-rule="evenodd" clip-rule="evenodd" d="M2.28933 8L8 2.28934L5.71065 0L0 5.71069V8H2.28933ZM5.71065 1.00951L6.9905 2.28934L6.06756 3.21227L4.78776 1.93243L5.71065 1.00951ZM4.28298 2.43719L5.56283 3.717L1.99366 7.28617H0.713832V6.00636L4.28298 2.43719Z" fill="black" />
																				</svg>

																			</>
																	}
																</div>
															</div>
															{
																articleInternalLinkEditableIndex !== index &&
																<svg className="remove-link" onClick={() => removeLinkHandler(link)} width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
																	<path fill-rule="evenodd" clip-rule="evenodd" d="M12 6C12 9.31368 9.31368 12 6 12C2.68629 12 0 9.31368 0 6C0 2.68629 2.68629 0 6 0C9.31368 0 12 2.68629 12 6ZM4.18178 4.18179C4.35751 4.00606 4.64243 4.00606 4.81818 4.18179L6 5.36358L7.18176 4.1818C7.3575 4.00607 7.64244 4.00607 7.81818 4.1818C7.99392 4.35754 7.99392 4.64246 7.81818 4.81818L6.63636 6L7.81818 7.18176C7.99392 7.3575 7.99392 7.64244 7.81818 7.81818C7.64244 7.99392 7.3575 7.99392 7.18176 7.81818L6 6.63642L4.81818 7.81818C4.64245 7.99392 4.35752 7.99392 4.18179 7.81818C4.00606 7.64244 4.00606 7.3575 4.18179 7.18182L5.36358 6L4.18178 4.81818C4.00604 4.64245 4.00604 4.35752 4.18178 4.18179Z" fill="black" fill-opacity="0.35" />
																</svg>
															}
														</div>
													);
												})
											}

											{
												!isProduction &&

												<div style={{ alignSelf: 'flex-start' }}>
													<Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
														{suggestedInternalLinks && suggestedInternalLinks.length > 0 ? (
															suggestedInternalLinks.map((suggestedLink, index) => {
																const truncatedPhrase = suggestedLink.phrase.length > 15
																	? suggestedLink.phrase.slice(0, 12) + '…'
																	: suggestedLink.phrase;

																const tooltipId = `tooltip-${index}`;

																return (
																	<div key={index}>
																		<span
																			data-tooltip-id={tooltipId}
																			data-tooltip-content={suggestedLink.link}
																			style={{ display: 'inline-block', }}
																			onClick={() => addSuggestedLinkHandler(suggestedLink)}
																		>
																			<Chip
																				label={truncatedPhrase}
																				variant="outlined"
																				size="small"
																				clickable
																			/>
																		</span>
																		<Tooltip id={tooltipId} place="bottom" style={{ zIndex: '3' }} />
																	</div>
																);
															})
														) : (
															<p>No suggested internal links available</p>
														)}
													</Box>
												</div>
											}

											{
												!processing &&
												<div className="generate-new-backlink internal-link">
													<svg width="11" height="11" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path opacity="0.15" d="M5.125 1C5.125 3.27817 6.97181 5.125 9.25 5.125C6.97181 5.125 5.125 6.97181 5.125 9.25C5.125 6.97181 3.27817 5.125 1 5.125C3.27817 5.125 5.125 3.27817 5.125 1Z" fill="black" />
														<path d="M5.125 1C5.125 3.27817 6.97181 5.125 9.25 5.125C6.97181 5.125 5.125 6.97181 5.125 9.25C5.125 6.97181 3.27817 5.125 1 5.125C3.27817 5.125 5.125 3.27817 5.125 1Z" stroke="black" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round" />
													</svg>
													<p className="generate-new-backlink-text" onClick={() => {
														if (active_website_domain) { // Check if active_website_domain is set
															setAddNewLinkModalActive({ active: true, linkType: "internal" });
														} else {
															setShowConnectWebsiteWarningModal(true); // Show warning modal if no active domain
														}
													}}>
														Add New Internal Link
													</p>
												</div>
											}
										</div>
									</div>

									{/* {
										!isProduction &&
										<div className="sidebar-items">
											<h6 onClick={() => {
												const suggestedInternalLinks = document.querySelector(".sidebar-item-contents.suggested-internal-links");
												const suggestedInternalLinksArrow = document.querySelector(".suggested-internal-links-arrow");
												if (suggestedInternalLinks) {
													suggestedInternalLinks.classList.toggle("expanded");
													suggestedInternalLinksArrow?.classList.toggle("active");
												}
											}}>
												<svg className="suggested-internal-links-arrow active" width="11" height="6" viewBox="0 0 11 6" fill="none" xmlns="http://www.w3.org/2000/svg">
													<path d="M0 4.4L5.5 0L11 4.4L10.1 5.6L5.5 2L1 5.6L0 4.4Z" fill="black" fill-opacity="0.5" />
												</svg>
												Suggested Internal Links
											</h6>
											<div className="sidebar-item-contents suggested-internal-links">
												{
													suggestedInternalLinks && suggestedInternalLinks.length > 0 ? (
														suggestedInternalLinks.map((suggestedLink, index) => {
															return (
																<div className="backlinks" key={index}>
																	<div className="backlink">
																		<span>{suggestedLink.phrase}</span>
																		<div className="backlink-url">
																			<a className="link" href={suggestedLink.link} target="_blank" rel="noreferrer">{suggestedLink.link}</a>
																		</div>
																	</div>
																	<AbunButton
																		type="primary"
																		className="add-suggested-link-btn is-small"
																		clickHandler={() => addSuggestedLinkHandler(suggestedLink)}
																	>
																		Add
																	</AbunButton>
																</div>
															);
														})
													) : (
														<p>No suggested internal links available</p>
													)
												}
											</div>
										</div>
									} */}

									{!processing &&

										<div className="sidebar-items">
											<h6 onClick={() => {
												const searchContainer = document.querySelector(".sidebar-item-contents.search-image-container");
												const searchArrow = document.querySelector(".search-image");
												if (searchContainer) {
													searchContainer.classList.toggle("expanded");
													searchArrow?.classList.toggle("active");
												}
											}}>
												<svg className="search-image active" width="11" height="6" viewBox="0 0 11 6" fill="none" xmlns="http://www.w3.org/2000/svg">
													<path d="M0 4.4L5.5 0L11 4.4L10.1 5.6L5.5 2L1 5.6L0 4.4Z" fill="black" fillOpacity="0.5" />
												</svg>
												Add More Images
												<span className="search-question"
													data-tooltip-id="style_button"
													data-tooltip-content="Double click the image or Drag and drop the image into your Article">?
												</span>
											</h6>
											<Tooltip id="style_button" place="bottom" />
											<div className="sidebar-item-contents search-image-container">
												<div className="search-image-input-wrapper">
													<input
														value={newSearchImage}
														className="input-search-image-container"
														type="text"
														placeholder="Enter the keyword and hit enter"
														onChange={(e) => setNewSearchImage(e.target.value)}
														onKeyDown={(e) => {
															if (e.key === "Enter" && newSearchImage.trim()) {
																searchImageHandler();
															}
														}}
													/>
												</div>
												<div className="image-grid">
													{searchImageUrl.slice(0, visibleCount).map((url, index) => (
														<img
															key={index}
															src={url.url}
															alt={url.alt || `${index} ${newSearchImage}`}
															className="search-image"
															onDoubleClick={() => {
																const editor = aiEditor.current;
																if (!editor) return;

																// Focus the editor first
																editor.focus();

																// Insert the image HTML
																editor.insert(`<img src="${url.url}" alt="${url.alt || "image"}" style="max-width: 100%;" />`);
															}}
														/>
													))}
												</div>


												{visibleCount < searchImageUrl.length && (
													<button
														className="load-more-button"
														onClick={() => setVisibleCount((prev) => prev + IMAGES_PER_LOAD)}
													>
														Load More Images
													</button>
												)}

											</div>
										</div>
									}

									<div className="sidebar-items">
										<h6 onClick={() => {
											const internalLinks = document.querySelector(".sidebar-item-contents.external-links");
											const internalLinksArrow = document.querySelector(".external-links-arrow");
											if (internalLinks) {
												internalLinks.classList.toggle("expanded");
												internalLinksArrow?.classList.toggle("active");
											}
										}}>
											<svg className="external-links-arrow active" width="11" height="6" viewBox="0 0 11 6" fill="none" xmlns="http://www.w3.org/2000/svg">
												<path d="M0 4.4L5.5 0L11 4.4L10.1 5.6L5.5 2L1 5.6L0 4.4Z" fill="black" fill-opacity="0.5" />
											</svg>
											External Links
										</h6>
										<div className="sidebar-item-contents external-links">
											{
												articleExternalLinks &&
												articleExternalLinks.map((link, index) => {
													return (
														<div className="backlinks" key={index}>
															<div className="backlink">
																<span>{link.linkTxt}</span>
																<div className="backlink-url">
																	{
																		articleExternalLinkEditableIndex === index ?
																			<input className="link-edit-input"
																				type="text"
																				value={link.linkHref}
																				onChange={(e) => {
																					saveEditedLinkHandler("external", link, e.target.value);
																				}}
																				onBlur={() => setArticleExternalLinkEditableIndex(-1)}
																				onKeyDown={(e) => {
																					if (e.key === "Enter") {
																						setArticleExternalLinkEditableIndex(-1);
																					}
																				}}
																			/> :
																			<>
																				<a className={`link ${articlePosted ? "posted" : ""}`} href={link.linkHref} target="_blank" rel="noreferrer">{link.linkHref}</a>

																				<svg className="edit-link" onClick={() => setArticleExternalLinkEditableIndex(index)} width="8" height="8" viewBox="0 0 8 8" fill="none" xmlns="http://www.w3.org/2000/svg">
																					<path fill-rule="evenodd" clip-rule="evenodd" d="M2.28933 8L8 2.28934L5.71065 0L0 5.71069V8H2.28933ZM5.71065 1.00951L6.9905 2.28934L6.06756 3.21227L4.78776 1.93243L5.71065 1.00951ZM4.28298 2.43719L5.56283 3.717L1.99366 7.28617H0.713832V6.00636L4.28298 2.43719Z" fill="black" />
																				</svg>

																			</>
																	}
																</div>
															</div>
															{
																articleExternalLinkEditableIndex !== index &&
																<svg className="remove-link" onClick={() => removeLinkHandler(link)} width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
																	<path fill-rule="evenodd" clip-rule="evenodd" d="M12 6C12 9.31368 9.31368 12 6 12C2.68629 12 0 9.31368 0 6C0 2.68629 2.68629 0 6 0C9.31368 0 12 2.68629 12 6ZM4.18178 4.18179C4.35751 4.00606 4.64243 4.00606 4.81818 4.18179L6 5.36358L7.18176 4.1818C7.3575 4.00607 7.64244 4.00607 7.81818 4.1818C7.99392 4.35754 7.99392 4.64246 7.81818 4.81818L6.63636 6L7.81818 7.18176C7.99392 7.3575 7.99392 7.64244 7.81818 7.81818C7.64244 7.99392 7.3575 7.99392 7.18176 7.81818L6 6.63642L4.81818 7.81818C4.64245 7.99392 4.35752 7.99392 4.18179 7.81818C4.00606 7.64244 4.00606 7.3575 4.18179 7.18182L5.36358 6L4.18178 4.81818C4.00604 4.64245 4.00604 4.35752 4.18178 4.18179Z" fill="black" fill-opacity="0.35" />
																</svg>
															}
														</div>
													);
												})
											}


											{
												!processing &&
												<div className="generate-new-backlink external-link">
													<svg width="11" height="11" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path opacity="0.15" d="M5.125 1C5.125 3.27817 6.97181 5.125 9.25 5.125C6.97181 5.125 5.125 6.97181 5.125 9.25C5.125 6.97181 3.27817 5.125 1 5.125C3.27817 5.125 5.125 3.27817 5.125 1Z" fill="black" />
														<path d="M5.125 1C5.125 3.27817 6.97181 5.125 9.25 5.125C6.97181 5.125 5.125 6.97181 5.125 9.25C5.125 6.97181 3.27817 5.125 1 5.125C3.27817 5.125 5.125 3.27817 5.125 1Z" stroke="black" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round" />
													</svg>
													<p className="generate-new-backlink-text" onClick={() => {
														setAddNewLinkModalActive({ active: true, linkType: "external" });
													}}>
														Add New External Link
													</p>
												</div>
											}
										</div>
									</div>

									<div className="sidebar-items">
										<h6 onClick={() => {
											const urlSlug = document.querySelector(".sidebar-item-contents.url-slug");
											const urlSlugArrow = document.querySelector(".url-slug-arrow");
											if (urlSlug) {
												urlSlug.classList.toggle("expanded");
												urlSlugArrow?.classList.toggle("active");
											}
										}}>
											<svg className="url-slug-arrow" width="11" height="6" viewBox="0 0 11 6" fill="none" xmlns="http://www.w3.org/2000/svg">
												<path d="M0 4.4L5.5 0L11 4.4L10.1 5.6L5.5 2L1 5.6L0 4.4Z" fill="black" fill-opacity="0.5" />
											</svg>
											URL / Slug
											<span className="search-question"
												data-tooltip-id="style_button"
												data-tooltip-content="URL / Slug">?
											</span>
										</h6>
										<Tooltip id="style_button" place="bottom" />
										<div className="sidebar-item-contents url-slug expanded">
											<div className="meta-description-content">
												<span contentEditable="true"
													onInput={(e) => {
														const el = e.currentTarget;
														if (el.innerText.length > 200) {
															errorAlertRef.current?.show("Slug cannot exceed 200 characters.");
															setTimeout(() => {
																errorAlertRef.current?.close();
															}, 5000);
															el.innerText = el.innerText.slice(0, 200);
														}
													}}
													onBlur={(event) => {
														const value = event.currentTarget.innerText;
														setarticleURLSlug(value.trim())
														setDisableSave(false)
													}}>{articleURLSlug || defaultSlug}</span>
											</div>
										</div>
									</div>

									{
										(selectedIntegration.includes("wordpress") || selectedIntegration.includes("ghl")) &&
										<div className="sidebar-items">
											<h6
												onClick={() => {
													const categoryLinks = document.querySelector(".sidebar-item-contents.categories");
													const categoryArrow = document.querySelector(".categories-arrow");
													if (categoryLinks) {
														categoryLinks.classList.toggle("expanded");
														categoryArrow?.classList.toggle("active");
													}
												}}
											>
												<svg className="categories-arrow active" width="11" height="6" viewBox="0 0 11 6" fill="none" xmlns="http://www.w3.org/2000/svg">
													<path d="M0 4.4L5.5 0L11 4.4L10.1 5.6L5.5 2L1 5.6L0 4.4Z" fill="black" fillOpacity="0.5" />
												</svg>
												Categories
											</h6>
											<div className="sidebar-item-contents categories">
												{categories.length > 0 ? (
													categories.map((category) => (
														<div className="backlinks" key={category.id}>
															<div className="backlink">
																<input
																	type="checkbox"
																	id={category.name}
																	checked={checkedCategories === category.id}
																	onChange={() => handleCheckboxChange(category.id)}
																	style={{ marginRight: '0.5rem', marginTop: '0.4rem' }}
																/>
																{/* <label>{category.name}</span> */}
																<label htmlFor={category.name}>{category.name}</label>
															</div>
														</div>
													))
												) : (
													<p>No categories available</p>
												)}
												{(selectedWordpressUrl || selectedGhlSite) && !processing && (
													<div className="generate-new-backlink internal-link">
														<svg width="11" height="11" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg">
															<path opacity="0.15" d="M5.125 1C5.125 3.27817 6.97181 5.125 9.25 5.125C6.97181 5.125 5.125 6.97181 5.125 9.25C5.125 6.97181 3.27817 5.125 1 5.125C3.27817 5.125 5.125 3.27817 5.125 1Z" fill="black" />
															<path d="M5.125 1C5.125 3.27817 6.97181 5.125 9.25 5.125C6.97181 5.125 5.125 6.97181 5.125 9.25C5.125 6.97181 3.27817 5.125 1 5.125C3.27817 5.125 5.125 3.27817 5.125 1Z" stroke="black" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round" />
														</svg>
														<p className="generate-new-backlink-text" onClick={() => {
															setAddNewCategoryModalActive({ active: true });
														}}>
															Add New Category
														</p>
													</div>
												)}
											</div>
										</div>
									}

									{/* Modal to Add New Category */}
									<AbunModal active={addNewCategoryModalActive.active}
										headerText="Add New Category"
										closeable={true}
										closeableKey={true}
										hideModal={() => {
											setAddNewCategoryModalActive({ active: false });
										}}>
										<div className="mt-4">
											{/* Category Name */}
											<div className="mb-4">
												<p className="mb-3 is-size-6">Enter Category Name*</p>
												<Input
													value={categoryName}
													className="add-category-container"
													type="text"
													placeholder="Enter category name"
													onChange={(value) => setCategoryName(value)}
												/>
											</div>

											{/* Category Description */}
											{!selectedGhlSite && (
												<div className="mb-4">
													<p className="mb-3 is-size-6">Description (optional)</p>
													<Input
														value={categoryDescription}
														className="add-category-container"
														type="text"
														placeholder="Enter description"
														onChange={(value) => setCategoryDescription(value)}
													/>
												</div>
											)}
											{/* Add Category Button */}
											<AbunButton
												type="success"
												disabled={isLoading || !categoryName}
												clickHandler={!selectedGhlSite ? addCategoryHandler : addGhlCategoryHandler}>
												{isLoading ? (
													<>
														Adding Category...
														<Icon iconName="spinner" additionalClasses={["icon-white", "mr-3"]} />
													</>
												) : (
													<>
														<Icon iconName="floppy-disk" additionalClasses={["icon-white", "mr-3"]} />
														Add Category
													</>
												)}
											</AbunButton>
										</div>
									</AbunModal>

									<div className="sidebar-items">
										<h6 onClick={() => {
											const metaDescription = document.querySelector(".sidebar-item-contents.meta-description");
											const metaDescriptionArrow = document.querySelector(".meta-description-arrow");
											if (metaDescription) {
												metaDescription.classList.toggle("expanded");
												metaDescriptionArrow?.classList.toggle("active");
											}
										}}>
											<svg className="meta-description-arrow" width="11" height="6" viewBox="0 0 11 6" fill="none" xmlns="http://www.w3.org/2000/svg">
												<path d="M0 4.4L5.5 0L11 4.4L10.1 5.6L5.5 2L1 5.6L0 4.4Z" fill="black" fill-opacity="0.5" />
											</svg>
											Meta Description
											<span className="search-question"
												data-tooltip-id="style_button"
												data-tooltip-content="meta description">?
											</span>
										</h6>
										<Tooltip id="style_button" place="bottom" />
										<div className="sidebar-item-contents meta-description expanded">
											<div className="meta-description-content">
												<span contentEditable="true"
													onInput={(e) => {
														const el = e.currentTarget;
														if (el.innerText.length > 200) {
															errorAlertRef.current?.show("Meta description cannot exceed 200 characters.");
															setTimeout(() => {
																errorAlertRef.current?.close();
															}, 5000);
															el.innerText = el.innerText.slice(0, 200);
														}
													}}
													onBlur={(event) => {
														const value = event.currentTarget.innerText.trim();
														if (value !== articleMetaDescription.trim()) {
															setArticleMetaDescription(value);
															saveContent(articleContent, value, articleFeedback);
															setDisableSave(false);
														}
													}}>{articleMetaDescription || defaultMetaDescription}</span>
												{
													!articlePosted && !processing &&
													<div className="copy-to-clipboard-container" onClick={() => {
														navigator.clipboard.writeText(articleMetaDescription);
														successAlertRef.current?.show("Meta Description copied to clipboard.");
														// Close the alert after 3 seconds
														setTimeout(() => {
															successAlertRef.current?.close();
														}, 3000);
													}}>
														<svg width="13" height="14" viewBox="0 0 13 14" fill="none" xmlns="http://www.w3.org/2000/svg">
															<path d="M2.8252 6.47539C2.8252 4.75467 2.8252 3.89431 3.35975 3.35975C3.89431 2.8252 4.75467 2.8252 6.47539 2.8252H8.30049C10.0212 2.8252 10.8815 2.8252 11.4161 3.35975C11.9507 3.89431 11.9507 4.75467 11.9507 6.47539V9.51722C11.9507 11.2379 11.9507 12.0983 11.4161 12.6328C10.8815 13.1674 10.0212 13.1674 8.30049 13.1674H6.47539C4.75467 13.1674 3.89431 13.1674 3.35975 12.6328C2.8252 12.0983 2.8252 11.2379 2.8252 9.51722V6.47539Z" stroke="#1C274C" />
															<path opacity="0.5" d="M2.8251 11.3422C1.81713 11.3422 1 10.5251 1 9.51712V5.86693C1 3.57263 1 2.42549 1.71274 1.71274C2.42549 1 3.57263 1 5.86693 1H8.30039C9.30839 1 10.1255 1.81713 10.1255 2.8251" stroke="#1C274C" />
														</svg>
														<span>Copy to clipboard</span>
													</div>
												}
											</div>
										</div>
									</div>
								</div >
							}

							{
								isOptionSidebarCollapsed === false &&
								isSmallScreen &&
								!articlePosted &&
								!processing &&
								<div className="is-flex">
									<div className="publish-post-options-for-small-screens">
										<h2>Ready to publish your post?</h2>
										<div className="publish-post-option-container">
											<svg
												className="close-options-icon"
												onClick={() => setIsOptionSidebarCollapsed(true)}
												xmlns="http://www.w3.org/2000/svg"
												viewBox="0 0 24 24"
												width="24"
												height="24"
												fill="none"
												stroke="currentColor"
												strokeWidth="2"
												strokeLinecap="round"
												strokeLinejoin="round"
												style={{ cursor: "pointer", position: "absolute", top: "10px", right: "10px" }}
											>
												<line x1="18" y1="6" x2="6" y2="18" />
												<line x1="6" y1="6" x2="18" y2="18" />
											</svg>
											<>
												{
													integrationWithUniqueID.length > 0 ?
														<>			
															<div className="publish-post-option">
																<input
																	type="radio"
																	id="publish"
																	name="publish"
																	value="publish"
																	checked={publishOption === "publish"}
																	onChange={(e) => {
																		setPublishOption(e.target.value);
																	}}
																/>
																<label htmlFor="publish-now" className="ml-2">Publish it live</label>
																<p>Publish this post immediately</p>
															</div>

															<div className="publish-post-option">
																<input
																	type="radio"
																	id="schedule"
																	name="schedule"
																	value="schedule"
																	checked={publishOption === "schedule"}
																	onChange={(e) => {
																		setPublishOption(e.target.value);
																	}}
																/>
																<label htmlFor="publish-later" className="ml-2">Schedule it for later</label>
																<p>Set automatic future publish date</p>
															</div>

															{publishOption === 'schedule' && (
																<div className="grid grid-cols-2 gap-4">
																	<div className="form-group">
																		<label htmlFor="publishDate">Date</label>
																		<input
																			type="date"
																			id="publishDate"
																			className="form-control"
																			defaultValue={articleScheduledForPosting ? articleScheduledDatetime?.format("YYYY-MM-DD") : schedulePublishDateTime?.format("YYYY-MM-DD")}
																			onChange={(e) => {
																				setSchedulePublishDateTime(dayjs(e.target.value + " " + schedulePublishTime))
																				setSchedulePublishDate(e.target.value);
																			}}
																		/>
																	</div>
																	<div className="form-group">
																		<label htmlFor="publishTime">Time</label>
																		<input
																			type="time"
																			id="publishTime"
																			className="form-control"
																			defaultValue={articleScheduledForPosting ? articleScheduledDatetime?.format("HH:mm") : schedulePublishDateTime?.format("HH:mm")}
																			onChange={(e) => {
																				setSchedulePublishDateTime(dayjs(schedulePublishDate + " " + e.target.value))
																				setSchedulePublishTime(e.target.value);
																			}}
																		/>
																	</div>
																</div>
															)}
															
															<div className="form-group">
																<label htmlFor="platform">{selectedIntegration?.split(" - ")[0]}</label>
																<Select
																	style={{ maxWidth: "100%" }}
																	labelId="platform-select-label"
																	id="platform-select-label"
																	value={selectedIntegrationUniqueID}
																	label="Platform"
																	onChange={(e) => setSelectedIntegrationUniqueID(e.target.value)}
																>
																	{integrationWithUniqueID.map((integration, index) => (
																		<MenuItem key={integration.integrationUniqueID} value={integration.integrationUniqueID}
																			onClick={() => {
																				setSelectedIntegrationUniqueID(integration.integrationUniqueID);
																				setIntegrationAndHideDropDownContent(integration.integrationName);
																			}}
																		>
																			{integration.integrationName.split(" - ")[1]}
																		</MenuItem>
																	))}
																</Select>
															</div>

															<div className="form-group">
																<label htmlFor="type">Type</label>
																<Select
																	fullWidth
																	labelId="type-select-label"
																	id="type-select-label"
																	value={publishType}
																	label="Type"
																	onChange={(e) => setPublishType(e.target.value)}
																>
																	<MenuItem value="publish">Published Post</MenuItem>
																	<MenuItem value="draft">Draft</MenuItem>
																</Select>
															</div>

															<button className="publish-post-btn"
																disabled={processing || articlePosted}
																onClick={() => {
																	if (articleUID) {
																		publishOption === 'publish' ? postToBlogHandler(articleUID) : scheduleArticleHandler(articleUID);
																	} else {
																		console.error("Post to Blog failed. Article UID missing.")
																	}
																}}
															>
																{publishOption === 'publish' ? 'Publish' : articleScheduledForPosting ? 'Reschedule' : 'Schedule'}
															</button>
														</> :
														<div className="card-content space-y-4">
															<p className="text-sm text-muted-foreground">
																Please integrate your blog to publish your article.
															</p>
															<div className="mt-2">
																<NavLink to={pageURL['new-integration']} className="text-blue-600 hover:text-blue-800 underline">
																	Go to Integrations
																</NavLink>
															</div>
														</div>
												}
												<div className="publish-post-option">
													<h3 className={`text-lg font-medium mb-4 ${integrationDone ? "integration-done-mt" : "mt-5"}`}>Copy Text as</h3>
													<div className="publish-post-option-contents">

														<div className="publish-post-option publish-post-option-for-small-screens">
															<input
																type="radio"
																id="copy-as-markdown"
																name="copy-as"
																value="markdown"
																checked={copyAs === "markdown"}
																onChange={(e) => {
																	setCopyAs(e.target.value);
																}}
															/>
															<label htmlFor="copy-as-markdown" className="ml-2" style={{ fontWeight: "400" }}>Markdown</label>
														</div>

														<div className="publish-post-option publish-post-option-for-small-screens">
															<input
																type="radio"
																id="copy-as-html"
																name="copy-as"
																value="html"
																checked={copyAs === "html"}
																onChange={(e) => {
																	setCopyAs(e.target.value);
																}}
															/>
															<label htmlFor="copy-as-html" className="ml-2" style={{ fontWeight: "400" }}>HTML</label>
														</div>

														<div className="publish-post-option publish-post-option-for-small-screens">
															<input
																type="radio"
																id="copy-as-raw-text"
																name="copy-as"
																value="raw-text"
																checked={copyAs === "raw-text"}
																onChange={(e) => {
																	setCopyAs(e.target.value);
																}}
															/>
															<label htmlFor="copy-as-raw-text" className="ml-2" style={{ fontWeight: "400" }}>Raw Text</label>
														</div>

														<button className="publish-post-copy-btn"
															onClick={() => {
																if (copyAs === "markdown") {
																	setRequestModalActive(true);
																	setModalText("Copying markdown...");

																	navigator.clipboard.writeText(aiEditorMarkdownContent as string)
																		.then(() => {
																			setRequestModalActive(false);
																			successAlertRef.current?.show("Markdown copied successfully!", 3000);
																			document.querySelector(".copy-txt-dropdown")?.classList.remove("show");
																		})
																		.catch(() => {
																			setRequestModalActive(false);
																			errorAlertRef.current?.show("Failed to copy markdown. Please try again.", 3000);
																		});
																} else if (copyAs === "html") {
																	setRequestModalActive(true);
																	setModalText("Processing HTML file...");
																	navigator.clipboard.writeText(aiEditorHtmlContent as string).then(() => {
																		setRequestModalActive(false);
																		setModalText("");
																		successAlertRef.current?.show("HTML copied successfully.");
																		setTimeout(() => {
																			successAlertRef.current?.close();
																		}, 3000);
																	}).catch(() => {
																		setRequestModalActive(false);
																		setModalText("");
																		// show error alert
																		errorAlertRef.current?.show("Failed to copy HTML. Please try again.");
																		// Close the alert after 3 seconds
																		setTimeout(() => {
																			errorAlertRef.current?.close();
																		}, 3000);
																	})
																	document.querySelector(".copy-txt-dropdown")?.classList.toggle("show");
																} else if (copyAs === "raw-text") {
																	navigator.clipboard.writeText(aiEditorTextContent as string).then(() => {
																		successAlertRef.current?.show("Text copied successfully.");
																		setTimeout(() => {
																			successAlertRef.current?.close();
																		}, 3000);
																	}).catch(() => {
																		errorAlertRef.current?.show("Failed to copy text. Please try again.");
																		setTimeout(() => {
																			errorAlertRef.current?.close();
																		}, 3000);
																	})
																	document.querySelector(".copy-txt-dropdown")?.classList.toggle("show");
																}
															}}
														>
															Copy
														</button>
													</div>
												</div>
											</>
										</div>
									</div>
								</div>
							}
							<AbunModal
								active={showUnsavedChangesModal}
								headerText="Save Changes"
								closeable={true}
								headerCenterStyle={{justifyContent: "center"}}
								hideModal={() => setShowUnsavedChangesModal(false)}
							>
								<div className="has-text-centered">
									<p>All changes you made will be lost. Do you still want to discard?</p>
									<div className="mt-4" style={{ display: "flex", alignItems: "center", justifyContent: "center", gap: "10px" }}>
										<AbunButton type="success"
											clickHandler={() => {
												handleSaveClick();
												setTimeout(() => {
													navigate(pageURL['showArticles']);
												}, 1500); // delay navigation by 1.5 seconds
											}}
										>
											Save & Exit
										</AbunButton>
										<AbunButton type="danger"
											clickHandler={() => { navigate(pageURL['showArticles']) }}
										>
											Discard
										</AbunButton>										
									</div>
								</div>

							</AbunModal>
						</div >
					</div >

					{/* ********** Talk With Your Article: Interactive Sidebar ********** */}
					{
						currTab === "twya" &&
						<div className="twya-sidebar-container w-80 bg-white p-4 rounded-2xl shadow-md">
							<h2 className="twya-sidebar-title text-lg font-bold flex items-center gap-2">✨ Article Assistant</h2>
							<div className="section mt-4">
								<div className="section-title">
									Selected Text
								</div>
								<div className="section-content">
									{twyaSelectedText}
								</div>
							</div>
							<div className="section mt-4">
								<div className="section-title">
									I want to:
								</div>
								<div className="section-content">
									{[
										"Shorten this para...",
										"Expand this para...",
										"Rewrite this para...",
										"Simplify this para...",
										"Make this more pro...",
										"Make this more cr...",
									].map((text) => (
										<button key={text} className="flex-1 bg-blue-100 px-3 py-2 rounded-md text-xs text-center">
											{text}
										</button>
									))}
								</div>
							</div>
							<div className="section mt-4">
								<div className="section-title">
									Give custom instructions...
								</div>
								<textarea className="w-full p-2 mt-3 border border-gray-300 rounded-md" placeholder="Give custom instructions..."
									onChange={(e) => setTwyaCustomInstructions(e.target.value)}
								/>
								<div className="flex items-center justify-end mt-3">
									<button className="bg-blue-500 text-white px-4 py-2 rounded-md" onClick={twyaSubmitHandler}>
										<svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24" fill="none">
											<path d="M20 7V8.2C20 9.88016 20 10.7202 19.673 11.362C19.3854 11.9265 18.9265 12.3854 18.362 12.673C17.7202 13 16.8802 13 15.2 13H4M4 13L8 9M4 13L8 17" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
										</svg>
									</button>
								</div>
							</div>
							<div className="section mt-4">
								<div className="logo-center">
									✨
								</div>
								<div className="mt-2">
									Select text and choose a suggestion or provide custom instructions.
								</div>
							</div>
						</div>
					}
				</div >
				<SuccessAlert ref={successAlertRef} />
				<ErrorAlert ref={errorAlertRef} />
			</>
		)
	}
}

function UploadFeaturedImage(props: UploadFeaturedImageProps) {
	// --------------------- STATES ------------------------
	const [selectedFile, setSelectedFile] = useState<File | null>(null);
	const [selectedFileURL, setSelectedFileURL] = useState<string | null>(null);
	const [uploading, setUploading] = useState<boolean>(false);

	// ---------------------------- MUTATIONS ----------------------------
	const uploadFeaturedImage = useMutation(uploadFeaturedImageMutation);

	// --------------------- FUNCTIONS ------------------------
	function handleFileSelection(event: React.ChangeEvent<HTMLInputElement>) {
		if (event.target.files && event.target.files.length > 0) {
			const file = event.target.files[0];
			setSelectedFile(file);
			setSelectedFileURL(URL.createObjectURL(file));
		}
	}

	async function uploadImage() {
		if (selectedFile) {
			setUploading(true);

			const base64 = await convertToBase64(selectedFile);
			const base64Data = (base64 as string).replace(/^data:image\/\w+;base64,/, '');

			// upload the image
			uploadFeaturedImage.mutate({
				articleUID: props.articleUid,
				image: base64Data,
			}, {
				onSuccess: (data) => {
					props.successAlertRef.current?.show("Featured image uploaded successfully.");
					props.setFeaturedImageURL(data.data.featured_image);
					props.hideModal();
					setSelectedFile(null);
					setSelectedFileURL(null);
					setUploading(false);
				},
				onError: () => {
					props.errorAlertRef.current?.show("Oops! Failed to upload :( Please try again later or contact us for further support.");
					setUploading(false);
				}
			});
		}
	}

	const convertToBase64 = (file) => {
		return new Promise((resolve, reject) => {
			const reader = new FileReader();
			reader.readAsDataURL(file);
			reader.onload = () => resolve(reader.result);
			reader.onerror = (error) => reject(error);
		});
	};

	// --------------------- MAIN JSX ------------------------
	return (
		<div className={"featured-image-upload"}>
			<div className={"featured-image-upload-container"}>
				<div className={"featured-image-upload-preview"}>
					{selectedFileURL ?
						<img src={selectedFileURL} alt={"featured"} /> :
						<img src={props.featuredImageURL} alt={"featured"} />
					}
				</div>
				<div className={"featured-image-upload-actions"}>
					<input type={"file"} accept={"image/*"} onChange={handleFileSelection} />
					<AbunButton className="mt-4" type={"success"} clickHandler={uploadImage} disabled={uploading}>
						{uploading ? "Uploading..." : "Upload"}
					</AbunButton>
				</div>
			</div>
		</div>
	)
}
