@import "../../assets/themes/mainTheme";
@import "../../assets/fonts/customFonts";

@import "bulma/sass/utilities/all";
@import "bulma/sass/base/all";
@import "bulma/sass/helpers/typography";
@import "bulma/sass/grid/columns";
@import "bulma/sass/components/card";
@import "bulma/sass/helpers/all";
@import "bulma/sass/elements/all";

@import "../../assets/bulma-overrides/bulmaOverrides";

.article-page-banner-card {
  background-color: $success !important;
}

.article-page-banner-title {
  font-size: 2.5rem;
  color: $white;
  font-family: $primary-font;
  font-weight: bold;
}

.article-page-banner-subtitle {
  font-size: 1.6rem;
  margin: 0;
  line-height: 1.4;
  color: darken($success, 26%);
  font-family: $secondary-font;
}

.article-page-banner-button {
  background-color: $white !important;
  color: $black !important;
  font-family: $secondary-font !important;
}

.articles-table-card {
  width: 100%;
}

.articles-page--no-website-message-box {
  width: 100%;
  max-width: 100%;
  text-align: center;
}

.keywords-articles-manual-upload {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.keywords-articles-csv-upload {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;

  .csv-div {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 300px;
    border: dashed 2px $grey-darker;
    border-radius: 12px;
    cursor: pointer;

    img {
      width: 100px;
      height: 100px;
      filter: invert(100%) sepia(1%) saturate(1230%) hue-rotate(326deg) brightness(111%) contrast(77%);
    }

    p {
      font: normal 1.4rem $primary-font;
      color: $grey-darker;
      margin: 0;
      text-align: center;

      &.csv-filename {
        font-size: 1rem;
      }
    }

    input {
      display: none;
    }
  }

  .column-selection-div {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: 2em;

    select {
      width: 250px;
    }
  }
}

.publish-to-blog-btn {
  border: none;
  background-color: transparent;
  cursor: pointer;

  img {
    width: 24px;
    height: auto;
  }
}

.view-on-blog-link {
  img {
    width: 24px;
    height: auto;
  }
}

button.hidden {
  display: none !important;
}

.publish-container {
  display: flex;
  align-items: center;

  .dropdown {
    position: relative;
    display: inline-block;

    .dropdown-icon {
      background-color: #fff;
      color: #000;
      border: none;
      cursor: pointer;
      // margin-left: 0.1rem;
      transition: transform 0.3s ease;
    }

    .rotate {
      transform: rotate(180deg);
    }

    .dropdown-content {
      display: none;
      position: absolute;
      background-color: #f9f9f9;
      min-width: 160px;
      box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
      z-index: 1;
      margin-top: 2px;
      margin-left: -8rem;
      overflow-y: auto;

      p {
        color: black;
        padding: 12px 16px;
        text-decoration: none;
        display: block;
        cursor: pointer;
      }

      p:hover {
        background-color: #f1f1f1;
      }

    }

    .show {
      display: block;
    }

  }

}

.upgrade-container {
  position: absolute;
  top: 45%;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  padding: 10px;
}

.back-links-container{

  h1 {
      font-family: $primary-font !important;
      font-size: 2rem !important;
      font-weight: 600;
  }

  p {
     color: rgba(0, 0, 0, .698);
      font-family: $secondary-font !important;
      font-size: 1.125rem !important;
  }
  .abun-table-responsive{

    tbody{
        tr td{
            text-decoration: none;
        }
    }
}

}