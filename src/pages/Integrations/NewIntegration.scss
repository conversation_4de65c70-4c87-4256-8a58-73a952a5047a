@import "bulma/sass/utilities/all";
@import "bulma/sass/helpers/all";
@import "bulma/sass/elements/all";

// Importing Settings.scss to use its styles
@import '../Settings/Settings.scss';

.integration-card {
  align-items: center;
  font-family: $primary-font;
  width: 100%;

  h1 {
    text-align: center;
    font-weight: 600 !important;
    font-family: $primary-font !important;
    font-size: 2rem !important;
  }

  p {
    color: rgba(0, 0, 0, .698);
    font-family: $secondary-font !important;
    font-size: 1.125rem !important;
  }

  .tabs {
    display: flex;
    width: 100% !important;
    max-width: 1000px;
    gap: 26px;
    justify-content: space-between !important;
    border-bottom: 1px solid #ccc;
    margin: 0 auto;

    @media (max-width: 769px) {
      overflow-x: auto;
      -ms-overflow-style: none;
      scrollbar-width: none;
    }
  }

  .tabs::-webkit-scrollbar {
    display: none;
  }

  .tab {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 3px;
    padding-right: 5px;
    border: none;
    background: none;
    color: #555;
    font-size: 20px;
    font-weight: 500;
    cursor: pointer;
    outline: none;
    transition: color 0.3s ease;
    font-family: $primary-font !important;
  }

  .tab.active {
    color: #007bff;
    border-bottom: 2px solid #007bff;
  }

  .tab img.integration-item-logo {
    width: 20px;
    height: 20px;
    object-fit: contain;
  }

  .tab.active::after {
    content: none;
  }

  .site-list {
    margin-top: 10px;

    @media (max-width: 1200px) {
      overflow-x: auto;
      white-space: nowrap;
    }

    .header {
      font-size: 18px;
    }

    .header-action-wordpress {
      font-size: 18px;
      justify-self: center;

      @media (max-width: 480px) {
        margin-left: 200px;
      }

      @media ((min-width: 480px) and (max-width: 769px)) {
        margin-left: 80px;
      }
    }

    .header-row {
      display: grid;
      justify-content: space-between;
      font-weight: bold;
      padding: 10px;
      border-bottom: 1px solid #ccc;
      grid-template-columns: 40px repeat(2, 1fr);
      color: $primary;

      @media (max-width: 1024px) {
        display: flex;
      }
    }

    .site-row {
      display: grid;
      justify-content: space-between;
      padding: 10px;
      border-bottom: 1px solid #f1f1f1;
      grid-template-columns: 40px repeat(2, 1fr);

      @media (max-width: 1024px) {
        display: flex;
      }

      .delete-btn {
        margin-right: 10px;
      }
    }

    .header-row-webflow {
      display: grid;
      justify-content: space-between;
      font-weight: bold;
      padding: 10px;
      border-bottom: 1px solid #ccc;
      grid-template-columns: 40px repeat(4, 1fr);
      color: $primary;

      @media (max-width: 1024px) {
        gap: 230px;
      }

      @media (max-width: 1024px) and (min-width: 768px) {
        .header-action-webflow {
          margin-left: -80px;
        }
      }
    }

    .site-row-webflow {
      display: grid;
      justify-content: space-between;
      padding: 10px;
      border-bottom: 1px solid #f1f1f1;
      grid-template-columns: 40px repeat(4, 1fr);

      @media (max-width: 1024px) {
        gap: 60px;
      }

      @media (max-width: 769px) {
        .delete-btn {
          margin-left: 40px;
        }
      }

      @media (max-width: 1024px) and (min-width: 768px) {
        .delete-btn {
          margin-left: 70px;
        }
      }
    }

    .header-action-webflow {
      font-size: 18px;
      // margin-left: 150px;
      justify-self: center;

      @media (max-width: 769px) {
        margin-left: -150px;
      }
    }

    .header-row-wix {
      display: grid;
      justify-content: space-between;
      font-weight: bold;
      padding: 10px;
      border-bottom: 1px solid #ccc;
      grid-template-columns: 40px repeat(3, 1fr);
      color: $primary;
      white-space: nowrap;

      @media (max-width: 1062px) {
        gap: 400px;
      }
    }

    .site-row-wix {
      display: grid;
      justify-content: space-between;
      padding: 10px;
      border-bottom: 1px solid #f1f1f1;
      grid-template-columns: 40px repeat(3, 1fr);

      @media (max-width: 1062px) {
        gap: 340px;
      }
    }

    .header-action-wix {
      font-size: 18px;

      @media (min-width: 1062px) {
        justify-self: center;
      }
    }

    .site-url {
      font-size: 14px;
      color: #333;
      white-space: nowrap;
      font-family: $secondary-font !important;
    }

    .delete-btn {
      background: none;
      border: none;
      font-size: 16px;
      color: red;
      cursor: pointer;
    }

    .delete-btn-wix {
      background: none;
      border: none;
      font-size: 16px;
      color: red;
      cursor: pointer;
    }

    .footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 20px;
    }

    .total {
      font-size: 16px;
      color: #333;
      gap: 5px;
      font-family: $secondary-font;

      @media (max-width: 500px) {
        display: flex;
        flex-wrap: wrap;
      }
    }

    .no-sites-connected {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      color: #555;
      margin-top: 10px;
    }

    .pagination {
      display: flex;
      align-items: center;
      gap: 8px;

      @media (max-width: 500px) {
        margin-top: 32px;
      }
    }

    .per-page {
      padding: 4px;
      border: 1px solid #ccc;
      border-radius: 4px;
      margin-left: 5px;
    }

    .page-numbers {
      font-size: 16px;
      color: #555;
    }

    .connect-btn-integration {
      padding: 10px 20px;
      background: $primary;
      color: #fff;
      border: none;
      border-radius: 4px;
      font-size: 16px;
      cursor: pointer;
      display: flex;
      font-family: $primary-font;

      @media (min-width: 1024px) {
        margin-right: 180px;
      }

      @media (max-width: 768px) {
        padding: 12px 32px;
      }

    }

    .connect-btn {
      padding: 10px 20px;
      background: $primary;
      color: #fff;
      border: none;
      border-radius: 4px;
      font-size: 16px;
      cursor: pointer;
      display: flex;
      justify-content: flex-end;
      margin-left: auto;
      margin-bottom: 20px;
      gap: 8px;
      font-family: $primary-font;

      @media (min-width: 1430px) {
        margin-right: 180px;
      }

      @media (max-width: 525px) {
        margin-top: 20px;
        margin-left: 0;
      }
    }

    .connect-btn:focus {
      border-color: #2684FF;
      outline: none !important;
      transition: all 100ms;
    }
  }

  .wp-failed-to-fetch-rest-route-error-box {
    background-color: #fff4f4;
    border: 1px solid #ffcdd2;
    border-radius: 8px;
    padding: 16px;
    margin-top: 1rem;

    h4 {
      color: #e53935;
      font-weight: bold;
      margin-bottom: 10px;
    }

    h5 {
      font-weight: bold;
      margin-bottom: 8px;
    }

    p {
      margin-top: 12px;
    }

    ul {
      padding-left: 20px;
      list-style: none;

      li {
        margin-bottom: 6px;
      }
    }
  }

  .integration-input {
    input {
      border-radius: 10px;
    }
  }

  .integration-modal-logo {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin-bottom: 1em;

    img {
      height: 47px;
      width: auto;
      object-fit: contain;

      &.plus {
        height: 25px;
        margin-left: 2em;
        margin-right: 2em;
      }
    }

    .abun-modal-logo {
      height: 35px !important;
    }
  }
}

// blur trial user
.integration-wrapper {
  position: relative;
  width: 100%;
}

.integration-blur-wrapper {
  position: relative;
  filter: blur(3px);
  opacity: 0.6;
  pointer-events: none;
  user-select: none;
}

/* Updated overlay without borders */
.integration-wrapper::after {
  content: '';
  position: fixed;
  /* Changed from absolute to fixed */
  top: 0;
  left: 0;
  width: 100vw;
  /* Full viewport width */
  height: 100vh;
  /* Full viewport height */
  pointer-events: none;
  z-index: 1;
  margin: 0;
  padding: 0;
  border: none;
  outline: none;
}

.integration-wrapper.trial-user-overlay::after {
  background-color: rgba(0, 0, 0, 0.5);
}

.integration-upgrade-modal {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 30;
  background: #fff;
  padding: 1.5rem;
  border-radius: 6px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  width: 350px;
  max-width: 90vw;

  h3 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
    text-align: center;
  }

  p {
    margin: 8px 0;
    font-size: 14px;
    color: #666;
    text-align: center;
    font-family: $secondary-font;
  }

  button {
    background-color: #2E54F9;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px;
    width: 100%;
    margin-top: 10px;
    cursor: pointer;
    font-size: 14px;
  }
}