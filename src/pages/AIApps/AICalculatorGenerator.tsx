import { useMutation, useQuery } from '@tanstack/react-query';
import { useEffect, useRef, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import AbunButton from "../../components/AbunButton/AbunButton";
import AbunModal from "../../components/AbunModal/AbunModal";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import { withAdminAndProductionCheck } from "../../utils/adminAndProductionCheck";
import {
    generateAICalculatorMutation,
    getAICalculatorDataQuery,
    getCalculatorScriptAndDivTag,
    modifyAICalculatorMutation,
    verifyAICalculatorMutation,
} from "../../utils/api";
import './AICalculatorGenerator.min.css';

type AICalculatorVersion = {
    id: number;
    version_name: string;
    html_code: string;
    created_on: string;
};

interface LocationState {
    calculatorId?: string;
    calculatorType?: string;
    userModifications?: string[];
}


function AICalculatorGenerator() {
    // --------------------------- HOOKS ---------------------------
    const location = useLocation();
    const navigate = useNavigate();
    const state = location.state as LocationState;

    // --------------------------- STATES ---------------------------
    const [calculatorId, setCalculatorId] = useState<string | null>(state?.calculatorId || null);
    const [calculatorType, setCalculatorType] = useState<string>(state?.calculatorType || "");
    const [calculatorHTML, setCalculatorHTML] = useState<string>("");
    const [userInput, setUserInput] = useState<string>("");
    const [activeTab, setActiveTab] = useState("update");
    const [selectedVersion, setSelectedVersion] = useState(0);
    const [isChecked, setIsChecked] = useState(true);
    const [pageUrl, setPageUrl] = useState('');
    const [embedCode, setEmbedCode] = useState({ scriptTag: '', divTag: '' });
    const [generatingEmbedCode, setGeneratingEmbedCode] = useState(false);
    const [verifyingEmbedCode, setVerifyingEmbedCode] = useState(false);
    const [calcDescription, setCalcDescription] = useState<string>("");
    const [isVerified, setIsVerified] = useState(false);
    const [versions, setVersions] = useState<Array<AICalculatorVersion>>([]);

    // --------------------------- REFS ---------------------------
    const errorAlertRef = useRef<any>(null);
    const successAlertRef = useRef<any>(null);
    const previewContainerRef = useRef<HTMLDivElement>(null);

    // --------------------------- QUERIES ---------------------------
    // Query for fetching calculator data
    const {
        isLoading: isLoadingCalculator
    } = useQuery({
        ...getAICalculatorDataQuery(state?.calculatorId || ''),
        enabled: !!state?.calculatorId,
        onSuccess: (response: any) => {
            if (response.data) {
                const { calculator_id, calc_type, code, script_tag, div_tag, url_restriction, is_verified, versions } = response.data.calculator_data;
                // Set calculator details
                setCalculatorId(calculator_id);
                setCalculatorType(calc_type);
                setCalculatorHTML(code);
                setEmbedCode({ scriptTag: script_tag, divTag: div_tag });
                setPageUrl(url_restriction);
                setIsVerified(is_verified);

                // Set versions if available
                if (versions && versions.length > 0) {
                    setVersions(versions);
                    // Set the latest version as selected by default
                    setSelectedVersion(versions[0].id);
                }
            }
        },
        onError: () => {
            errorAlertRef.current?.show("Failed to load calculator data");
        }
    });

    // -------------------------- MUTATIONS ---------------------------
    // Mutation for generating a new calculator
    const generateAICalculatorMut = useMutation(generateAICalculatorMutation);

    // Mutation for modifying an existing calculator
    const modifyAICalculatorMut = useMutation(modifyAICalculatorMutation);

    // Mutation for verifying calculator
    const verifyAICalculatorMut = useMutation(verifyAICalculatorMutation);

    // --------------------------- EFFECTS ---------------------------
    // Update preview when HTML changes and execute JavaScript
    useEffect(() => {
        if (previewContainerRef.current && calculatorHTML) {
            // Create an iframe
            const iframe = document.createElement('iframe');
            iframe.style.width = '100%';
            iframe.style.height = '100%';
            iframe.style.border = 'none';

            // Set the HTML content of the iframe
            iframe.srcdoc = calculatorHTML;

            // Clear previous content and append the iframe
            previewContainerRef.current.innerHTML = "";
            previewContainerRef.current.appendChild(iframe);
        }
    }, [calculatorHTML]);

    // --------------------------- HANDLERS ---------------------------
    const generateCalculator = () => {
        errorAlertRef.current?.close();
        successAlertRef.current?.close();

        if (!calculatorType || !calculatorType.trim()) {
            errorAlertRef.current?.show("Please enter a calculator type");
            return;
        }

        generateAICalculatorMut.mutate(
            { calc_type: calculatorType, calc_description: calcDescription },
            {
                onSuccess: (response) => {
                    const data = response.data;
                    if (data && data.status === "success") {
                        setCalculatorId(data.calculator_id);
                        setCalculatorHTML(data.html_content);
                        setCalculatorType(data.calc_type);
                        successAlertRef.current?.show("Calculator generated successfully!");
                        setTimeout(() => {
                            successAlertRef.current?.close();
                        }, 3000);
                    }
                },
                onError: () => {
                    errorAlertRef.current?.show(
                        "Failed to generate calculator. Please try again."
                    );
                }
            }
        );
    };

    const requestModification = () => {
        errorAlertRef.current?.close();
        successAlertRef.current?.close();

        if ((!userInput || !userInput.trim()) || !calculatorId) {
            return;
        }

        setUserInput("");

        modifyAICalculatorMut.mutate(
            { calculator_id: calculatorId, modifications: userInput },
            {
                onSuccess: (response) => {
                    const data = response.data;
                    if (data && data.status === "success") {
                        setCalculatorHTML(data.html_content);
                    }
                },
                onError: (error) => {
                    console.log(error);
                }
            }
        );
    };

    const copyToClipboard = (text: string) => {
        navigator.clipboard.writeText(text).then(() => {
            successAlertRef.current?.show("Copied to clipboard!");
            setTimeout(() => {
                successAlertRef.current?.close();
            }, 3000);
        }).catch(err => {
            errorAlertRef.current?.show("Failed to copy to clipboard.");
        });
    };

    const backToList = () => {
        navigate("/ai-calculators");
    };

    const handlePublish = () => {
        if (!calculatorId) {
            errorAlertRef.current?.show("Calculator ID is missing. Please generate a calculator first.");
            setTimeout(() => {
                errorAlertRef.current?.close();
            }, 3000);
            return;
        }

        if (!pageUrl || !pageUrl.trim()) {
            errorAlertRef.current?.show("Please enter the URL where you want to publish the calculator.");
            setTimeout(() => {
                errorAlertRef.current?.close();
            }, 3000);
            return;
        }

        try {
            new URL(pageUrl);
        } catch (_) {
            errorAlertRef.current?.show("Please enter a valid URL.");
            setTimeout(() => {
                errorAlertRef.current?.close();
            }, 3000);
            return;
        }

        setGeneratingEmbedCode(true);
        const queryParams = { calculator_id: calculatorId, url: pageUrl };
        getCalculatorScriptAndDivTag(queryParams).then(response => {
            if (response.success) {
                setEmbedCode({ scriptTag: response.script_tag, divTag: response.div_tag });
                successAlertRef.current?.show("Embed code generated successfully. Please add the following tags to your site.");
            } else {
                if (response.message) {
                    errorAlertRef.current?.show(response.message);
                } else {
                    errorAlertRef.current?.show("Failed to generate embed code. Please try again.");
                }
            }
        }).finally(() => {
            setGeneratingEmbedCode(false);
            setTimeout(() => {
                successAlertRef.current?.close();
                errorAlertRef.current?.close();
            }, 3000);
        });
    };

    const handleVerifyIntegration = () => {
        setVerifyingEmbedCode(true);
        verifyAICalculatorMut.mutate(
            { calculator_id: calculatorId },
            {
                onSuccess: (response) => {
                    if (response.success) {
                        successAlertRef.current?.show("Verification successful!");
                        setIsVerified(true);
                    } else {
                        errorAlertRef.current?.show(response.message);
                    }
                },
                onError: (error) => {
                    errorAlertRef.current?.show("Verification failed. Please try again.");
                },
                onSettled: () => {
                    setVerifyingEmbedCode(false);
                    setTimeout(() => {
                        successAlertRef.current?.close();
                        errorAlertRef.current?.close();
                    }, 3000);
                }
            }
        );
    };

    const handleVersionChange = (versionId: number) => {
        setSelectedVersion(versionId);
        const selectedVersionData = versions.find(v => v.id === versionId);
        if (selectedVersionData) {
            setCalculatorHTML(selectedVersionData.html_code);
        }
    };

    // Determine if any mutation is loading
    const isLoading = (state?.calculatorId && isLoadingCalculator) ||
        generateAICalculatorMut.isLoading ||
        modifyAICalculatorMut.isLoading;

    // --------------------------- RENDER ---------------------------
    return (
        <>
            {/* ------------------------------ PUBLISH MODAL ------------------------------ */}
            <AbunModal
                active={!embedCode.scriptTag && activeTab === "embed"}
                headerText="Publish Calculator"
                closeable={true}
                hideModal={() => {
                    setActiveTab('update');
                }}
                modalCardClass="publish-calculator-modal"
            >
                <div>
                    <label className="label">Enter the URL where you want to publish the calculator</label>
                    <input
                        type="text"
                        value={pageUrl}
                        onChange={(e) => setPageUrl(e.target.value)}
                        placeholder="ex. https://abun.com/pricing/pay-as-you-go"
                        className="input ai-calculator-type-input"
                    />
                    <AbunButton className="mt-3" disabled={generatingEmbedCode} type="primary" clickHandler={handlePublish}>
                        {generatingEmbedCode ? "Generating..." : "Generate Embed Code"}
                    </AbunButton>
                </div>
            </AbunModal>

            <div className="ai-calculator-generator-container">
                <div className="ai-calculator-generator-header">
                    <button className="ai-calculator-back-button" onClick={backToList}>
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M19 12H5" stroke="#000000" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                            <path d="M12 19L5 12L12 5" stroke="#000000" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>
                    </button>
                </div>

                <div className="ai-calculator-generator-content">
                    <h1 className="ai-calculator-generator-title">AI Calculator Generator</h1>
                    <p className="ai-calculator-generator-description">
                        Create Calculators for your Audience & Grow your Organic Traffic
                    </p>
                    <hr className="horizontal-line" />

                    {!calculatorHTML ? (
                        <div className="ai-calculator-input-card">
                            <div className="ai-calculator-input-group">
                                <label htmlFor="calculator-type"> <span>Create a</span>
                                    <input
                                        id="calculator-type"
                                        type="text"
                                        value={calculatorType}
                                        onChange={(e) => setCalculatorType(e.target.value)}
                                        placeholder="e.g., BMI, mortgage, tip, currency converter"
                                        className="ai-calculator-type-input"
                                        disabled={isLoading}
                                    /> <span>Calculator</span>
                                </label>
                            </div>
                            <p className="ai-calculator-text" >that does the following:</p>
                            <textarea
                                value={calcDescription}
                                onChange={(e) => setCalcDescription(e.target.value)}
                                className="textarea-box"
                                placeholder="(Optional)"
                            ></textarea>
                            <button
                                className="ai-generate-calculator-button"
                                onClick={generateCalculator}
                                disabled={isLoading}
                            >
                                Generate ➜
                            </button>
                        </div >
                    ) : (
                        <div className="ai-calculator-result-section">
                            <div className="ai-left-container">
                                <div className="tabs is-centered">
                                    <ul>
                                        <li className={activeTab === "update" ? "is-active" : ""}>
                                            <a onClick={() => setActiveTab("update")}>Update Calculator</a>
                                        </li>
                                        <li className={activeTab === "versions" ? "is-active" : ""}>
                                            <a onClick={() => setActiveTab("versions")}>Versions</a>
                                        </li>
                                        <li className={activeTab === "embed" ? "is-active" : ""}>
                                            <a onClick={() => setActiveTab("embed")}>Get Embed Code</a>
                                        </li>
                                    </ul>
                                </div>
                                <div className="box" style={{ marginTop: "40px" }}>

                                    {activeTab === "update" &&

                                        <div className="ai-calculator-chat-container">
                                            <div className="ai-content">
                                                <h2 className="title-text">What changes do you want in the calculator?</h2>
                                                <textarea
                                                    className="textarea custom-textarea"
                                                    placeholder="In the list include Draftss.com on the top..."
                                                    value={userInput}
                                                    onChange={(e) => setUserInput(e.target.value)}
                                                />
                                                <button
                                                    className="button is-primary custom-button"
                                                    onClick={requestModification}
                                                    disabled={isLoading || (!userInput || !userInput.trim())}
                                                >
                                                    {isLoading ? "Updating..." : "Update Calculator"}
                                                </button>
                                                <div className="field mt-6">
                                                    <label className="checkbox">
                                                        <input
                                                            type="checkbox"
                                                            checked={isChecked}
                                                            onChange={() => setIsChecked(!isChecked)}
                                                            className="mr-2"
                                                        />
                                                        Make the Calculator Name as H1 Tag
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    }

                                    {activeTab === "versions" &&
                                        <div className="ai-version-container">
                                            {versions.length > 0 ? (
                                                versions.map((version) => (
                                                    <label key={version.id} className="is-flex is-align-items-center mb-3 ml-0" style={{ cursor: "pointer" }}>
                                                        <input
                                                            type="radio"
                                                            name="version"
                                                            value={version.id}
                                                            checked={selectedVersion === version.id}
                                                            onChange={() => handleVersionChange(version.id)}
                                                            className="mr-2 custom-radio"
                                                        />
                                                        <div>
                                                            <p style={{ fontWeight: '500' }}>{version.version_name}</p>
                                                            <p className="is-size-7 has-text-grey">{version.created_on}</p>
                                                        </div>
                                                    </label>
                                                ))
                                            ) : (
                                                <p>No versions available for this calculator.</p>
                                            )}
                                        </div>
                                    }
                                    {activeTab === "embed" && (
                                        <div className="ai-embed-container">
                                            <div className="ai-section">
                                                <h3 className="subtitle">Add this to the &lt;head&gt; tag of your <a href={pageUrl} target="_blank" rel="noopener noreferrer" style={{ display: "inline" }}>selected</a> page</h3>
                                                <textarea
                                                    className="textarea"
                                                    value={embedCode.scriptTag}
                                                    readOnly
                                                />
                                                <button
                                                    className="copy-btn button is-primary"
                                                    onClick={() => copyToClipboard(embedCode.scriptTag)}
                                                >
                                                    Copy
                                                </button>
                                            </div>
                                            <div className="ai-section">
                                                <h3 className="subtitle mt-4">
                                                    Add this tag to your <a href={pageUrl} target="_blank" rel="noopener noreferrer" style={{ display: "inline" }}>selected</a> page
                                                </h3>
                                                <textarea
                                                    className="textarea"
                                                    value={embedCode.divTag}
                                                    readOnly
                                                />
                                                <button
                                                    className="copy-btn button is-primary"
                                                    onClick={() => copyToClipboard(embedCode.divTag)}
                                                >
                                                    Copy
                                                </button>
                                            </div>

                                            {!isVerified &&
                                                <div className="ai-section mt-4">
                                                    <AbunButton
                                                        type="primary"
                                                        className="verify-btn"
                                                        disabled={verifyingEmbedCode}
                                                        clickHandler={handleVerifyIntegration}
                                                    >
                                                        {verifyingEmbedCode ? "Verifying..." : "Verify Integration"}
                                                    </AbunButton>
                                                </div>
                                            }
                                        </div>
                                    )}
                                </div>
                            </div>

                            <hr className="ai-horizontal-line" />

                            <div className="ai-calculator-preview-section">
                                <div className="ai-preview-container">
                                    <div className="ai-preview-content" ref={previewContainerRef}></div>
                                </div>
                            </div>
                        </div>
                    )
                    }
                </div >

                <ErrorAlert ref={errorAlertRef} />
                <SuccessAlert ref={successAlertRef} />
            </div >

        </>
    );
};

export default withAdminAndProductionCheck(AICalculatorGenerator);
