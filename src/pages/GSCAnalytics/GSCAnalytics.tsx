import { useMutation } from "@tanstack/react-query";
import { useRef, useState } from "react";
import { useLoaderData, useNavigate } from "react-router-dom";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import { useUIState } from "../../hooks/UIStateContext";
import { withAdminAndProductionCheck } from "../../utils/adminAndProductionCheck";
import { googleIntegrationMutation } from "../../utils/api";
import { LazyLoadGSCDomainsList } from "../KeywordsResearchV2/GSC/LazyGSCComponents";
import { PageData } from "../KeywordsResearchV2/KeywordResearch";
import { pageURL } from "../routes";
import './GSCAnalytics.min.css';
import GSCData from "./GSCData";

interface GoogleIntegrationResponse {
    success: boolean
    authorization_endpoint: string
    current_plan_name: string;
}

const GSCAnalytics = () => {
    const { pageData } = useLoaderData() as {
        pageData: PageData;
    };
    // const [selectedDomain, setSelectedDomain] = useState("");
    const [selectedDomain, setSelectedDomain] = useState<string>(pageData.selected_gsc_domain || pageData.current_active_website || "");
    const [GSCIntegrationProcessing, setGSCIntegrationProcessing] = useState(false);
    const { hamburgerActive } = useUIState();


    // --------------------------- REFS ---------------------------
    const failAlertRef = useRef<any>(null);
    const successAlertRef = useRef<any>(null);
    const googleIntegrationMut = useMutation(googleIntegrationMutation);

    const navigate = useNavigate();

    function handleBackBtnClick() {
        navigate('/create-article');
    }

    function googleIntegration(integrationType: "google-search-console" | "google-analytics" | "google-drive") {
        failAlertRef.current?.close();
        setGSCIntegrationProcessing(true);
        googleIntegrationMut.mutate(integrationType, {
            onSuccess: (data) => {
                const response: GoogleIntegrationResponse = data['data'];
                if (response.success) {
                    successAlertRef.current?.show("New GSC Account Integrated successfully.");
                    window.location.href = response.authorization_endpoint;
                } else {
                    setGSCIntegrationProcessing(false);
                    failAlertRef.current?.show(
                        "Oops! Something went wrong :( Please try " +
                        "again later or contact us for further support."
                    );
                }
            },
            onError: () => {
                setGSCIntegrationProcessing(false);
                failAlertRef.current?.show(
                    "Oops! Something went wrong :( Please try " +
                    "again later or contact us for further support."
                );
            }
        })
    }

    return (
        <div className={"gsc-wrapper " + (pageData.current_plan_name === "Trial" ? "trial-user-overlay" : "")}>
            <div className="gsc-container">
                <div className={"is-flex is-justify-content-center is-align-items-center is-flex-direction-column  has-text-centered"}>
                    <h1>Google Search Console Insights for {selectedDomain}</h1>
                    {pageData.has_gsc_integration ? "" : <p className={"is-size-6 mt-2 mb-4"}>Connect to your Google Search Console account to analyze search performance</p>}
                </div>
                {
                    pageData.has_gsc_integration ?
                        (!selectedDomain ?
                            <LazyLoadGSCDomainsList failAlertRef={failAlertRef} setSelectedDomain={setSelectedDomain} handleBackBtnClick={handleBackBtnClick} />
                            :
                            <GSCData selectedDomain={selectedDomain} handleBackBtnClick={() => {
                                setSelectedDomain("");
                            }} />
                        )
                        :
                        <div className="gsc-box">
                            <h2>Connect Google Search Console</h2>
                            <p>Connect your GSC account to visualize your search performance data</p>
                            <div className="gsc-benefits">
                                <p className="benefits-title">Benefits of connecting GSC:</p>
                                <ul>
                                    <li>Visualize your search performance metrics</li>
                                    <li>Identify top-performing keywords and pages</li>
                                    <li>Track your position in search results over time</li>
                                    <li>Discover optimization opportunities</li>
                                </ul>
                            </div>
                            <div className="gsc-privacy">
                                <span className="privacy-icon">🔒</span>
                                <div className="privacy-text">
                                    <span className="privacy-title">Your data stays private</span>
                                    <p>We only access the data you authorize. You can revoke access at any time.</p>
                                </div>
                            </div>
                            <button className="gsc-connect-button" onClick={() => googleIntegration("google-search-console")}
                                disabled={GSCIntegrationProcessing}>{GSCIntegrationProcessing ? "Connecting..." : "Connect Google Search Console"}</button>
                        </div>
                }
            </div>

            {!hamburgerActive && pageData.current_plan_name === "Trial" && (
                <div className="gsc-upgrade-modal">
                    <h3>Upgrade to Unlock GSC Analytics.</h3>
                    <p>Upgrade your plan to access all features.</p>
                    <button onClick={() => navigate(pageURL["manageSubscription"])}>
                        See Plans →
                    </button>
                </div>
            )}
        </div>
    );
}

export default GSCAnalytics