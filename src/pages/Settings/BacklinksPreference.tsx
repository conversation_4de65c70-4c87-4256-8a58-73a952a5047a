import { useMutation } from "@tanstack/react-query";
import React, { Dispatch, MutableRefObject, SetStateAction, useEffect, useState } from "react";
import { useRouteLoaderData } from "react-router-dom";
import { BasePageData } from "../../pages/Base/Base";
import { saveSettingsMutation } from "../../utils/api";

interface ExternalBacklinksProps {
    errorAlertRef: MutableRefObject<any>
    successAlertRef: MutableRefObject<any>
    external_backlinks_preference: "no-follow" | "off" | "follow"
    max_internal_backlinks: number
    max_external_backlinks: number
    internal_glossary_backlinks_preference: "on" | "off"
    max_internal_glossary_backlinks: number
    updatePageData: () => void
    setUnsavedChanges: Dispatch<SetStateAction<boolean>>
}

export default function BacklinksPreference(props: ExternalBacklinksProps) {
    // --------------------- STATES ---------------------
    const [externalBacklinksPreference, setExternalBacklinksPreference] = useState<string>(props.external_backlinks_preference);
    const [maxInternalBacklinks, setMaxInternalBacklinks] = useState<number>(props.max_internal_backlinks);
    const [maxExternalBacklinks, setMaxExternalBacklinks] = useState<number>(props.max_external_backlinks);
    const [internalGlossaryBacklinksPreference, setInternalGlossaryBacklinksPreference] = useState<string>(props.internal_glossary_backlinks_preference);
    const [maxInternalGlossaryBacklinks, setMaxInternalGlossaryBacklinks] = useState<number>(props.max_internal_glossary_backlinks);

    // --------------------- ADMIN CHECK ---------------------
    const basePageData = useRouteLoaderData("base") as BasePageData;
    const isAdmin = basePageData?.is_admin;
    const isProduction = process.env.REACT_APP_DRF_DOMAIN === "https://api.abun.com";

    const saveSettings = useMutation(saveSettingsMutation);
    // ---------------------------- MUTATIONS ----------------------------

    // --------------------- FUNCTIONS ---------------------
    const handleBacklinksPreferenceChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const BacklinksPreference = event.target.value;
        if (
            BacklinksPreference === "off" ||
            BacklinksPreference === "no-follow" ||
            BacklinksPreference === "follow"
        ) {
            if (BacklinksPreference === "off") {
                document.querySelector(".max-ext-backlinks")?.classList.add("is-hidden");
            } else {
                document.querySelector(".max-ext-backlinks")?.classList.remove("is-hidden");
                setMaxExternalBacklinks(Math.max(1, Math.min(maxExternalBacklinks, 10)));
            }
            setExternalBacklinksPreference(BacklinksPreference);
        }
    };

    const handleInternalGlossaryBacklinksPreferenceChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const BacklinksPreference = event.target.value;
        if (
            BacklinksPreference === "off" ||
            BacklinksPreference === "on"
        ) {
            if (BacklinksPreference === "off") {
                document.querySelector(".max-internal-glossary")?.classList.add("is-hidden");
            } else {
                document.querySelector(".max-internal-glossary")?.classList.remove("is-hidden");
                setMaxInternalGlossaryBacklinks(Math.max(1, Math.min(maxInternalGlossaryBacklinks, 10)));
            }
            setInternalGlossaryBacklinksPreference(BacklinksPreference);
        }
    };

    const saveBacklinkSettings = () => {
        saveSettings.mutate(
            {
                settingsToSave: [
                    { settingName: "external_backlinks_preference", settingValue: externalBacklinksPreference },
                    { settingName: "max_internal_backlinks", settingValue: maxInternalBacklinks },
                    { settingName: "max_external_backlinks", settingValue: maxExternalBacklinks },
                    { settingName: "internal_glossary_backlinks_preference", settingValue: internalGlossaryBacklinksPreference },
                    { settingName: "max_internal_glossary_backlinks", settingValue: maxInternalGlossaryBacklinks }
                ]
            },
            {
                onSuccess: () => {
                    props.updatePageData();
                    props.setUnsavedChanges(false);
                    props.successAlertRef.current?.show("Changes Saved!");
                    setTimeout(() => {
                        try {
                            if (props.successAlertRef.current) {
                                props.successAlertRef.current.close();
                            }
                        } catch (e) { }
                    }, 3000);

                },
                onError: () => {
                    props.errorAlertRef.current?.show("Oops! Something went wrong :( Please try again later or contact us for further support.");
                }
            }
        );
    };

    // Trigger save only when values change
    useEffect(() => {
        if (
            props.external_backlinks_preference !== externalBacklinksPreference ||
            props.max_internal_backlinks !== maxInternalBacklinks ||
            props.max_external_backlinks !== maxExternalBacklinks ||
            props.internal_glossary_backlinks_preference !== internalGlossaryBacklinksPreference ||
            props.max_internal_glossary_backlinks !== maxInternalGlossaryBacklinks
        ) {
            const timeout = setTimeout(() => {
                saveBacklinkSettings();
            }, 500);

            return () => clearTimeout(timeout); // Clear timeout if values change again
        }
    }, [externalBacklinksPreference, maxInternalBacklinks, maxExternalBacklinks, maxInternalGlossaryBacklinks, internalGlossaryBacklinksPreference]);

    // =====================================================
    // --------------------- MAIN CODE ---------------------
    // =====================================================
    return (
        <div className="settings-sections">
            <div className="settings-section backlinks-settings">
                <div className="pb-5">
                    <h5 className="settings-section-title">Internal Backlinks Preference for Articles:</h5>
                    {/* set total internal backlinks max 10 */}
                    <div className="mt-5">
                        {/* Added flex propery */}
                        <div className={"field is-horizontal is-flex"}>
                            <label className="label sub-title">Maximum number of internal backlinks per article:</label>
                            <input
                                type="number"
                                className="input max-backlinks-input ml-4 mr-3 is-small"
                                min="1"
                                max="10"
                                value={maxInternalBacklinks}
                                onChange={(event) => {
                                    const newValue = Math.max(1, Math.min(Number(event.target.value), 10));
                                    setMaxInternalBacklinks(newValue);
                                }}
                                onKeyDown={(event) => {
                                    if (event.key === 'Backspace') {
                                        event.preventDefault();
                                        setMaxInternalBacklinks(1);
                                    }
                                }}
                                onKeyPress={(event) => {
                                    if (event.key !== 'Backspace') {
                                        // do not allow any inputs other than numbers
                                        if (isNaN(Number(event.key))) {
                                            event.preventDefault();
                                        } else if (Number(event.key) === 0) {
                                            event.preventDefault();
                                            setMaxInternalBacklinks(10);
                                        } else {
                                            event.preventDefault();
                                            const newValue = Math.max(1, Math.min(Number(event.key), 10));
                                            setMaxInternalBacklinks(Number(newValue));
                                        }
                                    }
                                }}
                            />
                        </div>
                    </div>
                </div>
                <div className="pb-5">
                    <h5 className="settings-section-title">External Backlinks Preference for Articles:</h5>
                    <div className="control mt-5">
                        <label className="radio">
                            <input
                                type="radio"
                                className="mr-4"
                                name="preference"
                                value="off"
                                onChange={handleBacklinksPreferenceChange}
                                checked={externalBacklinksPreference === 'off'}
                            />
                            <span className="settings-radio-item-title">Do not add external links</span>
                        </label>
                    </div>
                    <div className="control mt-3">
                        <label className="radio">
                            <input
                                type="radio"
                                className="mr-4"
                                name="preference"
                                value="follow"
                                onChange={handleBacklinksPreferenceChange}
                                checked={externalBacklinksPreference === 'follow'}
                            />
                            <span className="settings-radio-item-title">Add external links (follow)</span>
                        </label>
                    </div>
                    <div className="control mt-3">
                        <label className="radio">
                            <input
                                type="radio"
                                className="mr-4"
                                name="preference"
                                value="no-follow"
                                onChange={handleBacklinksPreferenceChange}
                                checked={externalBacklinksPreference === 'no-follow'}
                            />
                            <span className="settings-radio-item-title">Add external links (no-follow)</span>
                        </label>
                    </div>
                    <div className={`mt-5 max-ext-backlinks ${externalBacklinksPreference === 'off' ? 'is-hidden' : ''}`}>
                        {/* Added flex propery */}
                        <div className={"field is-horizontal is-flex"}>
                            <label className="label sub-title">Maximum number of external backlinks per article:</label>
                            <input
                                type="number"
                                className="input max-backlinks-input ml-4 mr-3 is-small"
                                min="1"
                                max="10"
                                value={maxExternalBacklinks}
                                onChange={(event) => {
                                    const newValue = Math.max(1, Math.min(Number(event.target.value), 10));
                                    setMaxExternalBacklinks(newValue);
                                }}
                                onKeyDown={(event) => {
                                    if (event.key === 'Backspace') {
                                        event.preventDefault();
                                        setMaxExternalBacklinks(1);
                                    }
                                }}
                                onKeyPress={(event) => {
                                    if (event.key !== 'Backspace') {
                                        // do not allow any inputs other than numbers
                                        if (isNaN(Number(event.key))) {
                                            event.preventDefault();
                                        } else if (Number(event.key) === 0) {
                                            event.preventDefault();
                                            setMaxExternalBacklinks(10);
                                        } else {
                                            event.preventDefault();
                                            const newValue = Math.max(1, Math.min(Number(event.key), 10));
                                            setMaxExternalBacklinks(Number(newValue));
                                        }
                                    }
                                }}
                            />
                        </div>
                    </div>
                </div>
                {(!isProduction || isAdmin) && (
                    <div className="pb-5">
                        <h5 className="settings-section-title">Internal Backlinks Preference for Glossary Content:</h5>
                        <div className="control mt-5">
                            <label className="radio">
                                <input
                                    type="radio"
                                    className="mr-4"
                                    name="internal-glossary-preference"
                                    value="off"
                                    onChange={handleInternalGlossaryBacklinksPreferenceChange}
                                    checked={internalGlossaryBacklinksPreference === 'off'}
                                />
                                <span className="settings-radio-item-title">Do not add Internal links</span>
                            </label>
                        </div>
                        <div className="control mt-3">
                            <label className="radio">
                                <input
                                    type="radio"
                                    className="mr-4"
                                    name="internal-glossary-preference"
                                    value="on"
                                    onChange={handleInternalGlossaryBacklinksPreferenceChange}
                                    checked={internalGlossaryBacklinksPreference === 'on'}
                                />
                                <span className="settings-radio-item-title">Add Internal links</span>
                            </label>
                        </div>
                        <div className={`mt-5 max-internal-glossary field is-horizontal is-flex ${internalGlossaryBacklinksPreference === 'off' ? 'is-hidden' : ''}`}>
                            <label className="label sub-title">Maximum number of internal backlinks per Glossary Content:</label>
                            <input
                                type="number"
                                className="input max-backlinks-input ml-4 mr-3 is-small"
                                min="1"
                                max="10"
                                value={maxInternalGlossaryBacklinks}
                                onChange={(event) => {
                                    const newValue = Math.max(1, Math.min(Number(event.target.value), 10));
                                    setMaxInternalGlossaryBacklinks(newValue);
                                }}
                                onKeyDown={(event) => {
                                    if (event.key === 'Backspace') {
                                        event.preventDefault();
                                        setMaxInternalGlossaryBacklinks(1);
                                    }
                                }}
                                onKeyPress={(event) => {
                                    if (event.key !== 'Backspace') {
                                        // do not allow any inputs other than numbers
                                        if (isNaN(Number(event.key))) {
                                            event.preventDefault();
                                        } else if (Number(event.key) === 0) {
                                            event.preventDefault();
                                            setMaxInternalGlossaryBacklinks(10);
                                        } else {
                                            event.preventDefault();
                                            const newValue = Math.max(1, Math.min(Number(event.key), 10));
                                            setMaxInternalGlossaryBacklinks(Number(newValue));
                                        }
                                    }
                                }}
                            />
                        </div>
                    </div>
                )}
            </div>

            {/* -=-=-=-=-=-=-=-=-=-=-= Save All Settings Button -=-=-=-=-=-=-=-=-=-=-= */}
            {/* <div className="save-changes-section mt-4">
                <button
                    className={`button is-primary ${saveSettings.isLoading ? 'is-loading' : ''}`}
                    onClick={saveBacklinkSettings}
                >
                    Save Changes
                </button>
            </div> */}
        </div>
    );
}