import { useMutation, useQuery } from "@tanstack/react-query";
import { ColumnDef, createColumnHelper } from '@tanstack/react-table';
import { useEffect, useRef, useState } from "react";
import { useNavigate } from 'react-router-dom';
import AbunLoader from '../../components/AbunLoader/AbunLoader';
import AbunModal from '../../components/AbunModal/AbunModal';
import AbunTable from "../../components/AbunTable/AbunTable";
import ErrorAlert from '../../components/ErrorAlert/ErrorAlert';
import LinkButton from "../../components/LinkButton/LinkButton";
import SuccessAlert from '../../components/SuccessAlert/SuccessAlert';
import { withAdminAndProductionCheck } from "../../utils/adminAndProductionCheck";
import { getPublishedArticle, getTaskProgress, retryFn, wpPublishedArticleMutation } from "../../utils/api";
import './OptimizePublishArticle.min.css';

export interface PublishedArticle {
  id: number;
  post_id: number;
  media_id: number | null;
  title: string;
  slug: string;
  url: string;
  published_date: string;
  gsc_position: string;
  article_uid: string;
}

const OptimizePublishedArticle = () => {
  const navigate = useNavigate()
  const pageSizes = [5, 10, 15, 30, 50, 100, 500];
  const successAlertRef = useRef<any>(null);
  const errorAlertRef = useRef<any>(null);
  const tableRef = useRef<{ refetchData: () => Promise<void> }>(null);

  const [tableData, setTableData] = useState<PublishedArticle[]>([]);
  const [requestModalActive, setRequestModalActive] = useState(false);
  const [modalText, setModalText] = useState("");

  // ----------------------- NON STATE CONSTANTS -----------------------
  const wpPublishedArticleFetch = useMutation(wpPublishedArticleMutation);

  const PublishedArticle = useQuery({
    queryKey: ['getPublishedArticle'],
    queryFn: getPublishedArticle,
    cacheTime: 0,
    refetchOnWindowFocus: false,
    retry: retryFn,
  });

  useEffect(() => {
    if (PublishedArticle.isSuccess && (PublishedArticle.data as any).data.articles?.length > 0) {
      setTableData((PublishedArticle.data as any).data.articles);  // Use data.articles from response
    }
  }, [PublishedArticle.isSuccess, PublishedArticle.data]);

  useEffect(() => {
    const articles = PublishedArticle.data?.data?.articles || [];
    const isDataFetched = PublishedArticle.isSuccess;
    const isTableEmpty = articles.length === 0;
    const canFetch = !wpPublishedArticleFetch.isLoading && !wpPublishedArticleFetch.isSuccess && !wpPublishedArticleFetch.isError;

    if (isDataFetched && isTableEmpty && canFetch) {
      setRequestModalActive(true);
      wpPublishedArticleFetch.mutate(undefined, {
        onSuccess: (data) => {
          const taskId = data?.data?.task_id;
          if (taskId) {
            pollTaskProgress(taskId);
          } else {
            setRequestModalActive(false);
            errorAlertRef.current?.show("Failed to get task ID.");
            setTimeout(() => errorAlertRef.current?.close(), 5000);
          }
        },
        onError: (error) => {
          setRequestModalActive(false);
          if (error?.response.data.err_id === 'NO_WP_INTEGRATION') {
            errorAlertRef.current?.show("No Wordpress integration found.")
          } else if (error?.response.data.err_id === 'NO_GSC_INTEGRATION') {
            errorAlertRef.current?.show("No GSC integration found.");
          } else {
            errorAlertRef.current?.show("Failed to start task.")
          }
          setTimeout(() => errorAlertRef.current?.close(), 5000);
        }
      });
    }
  }, [PublishedArticle.isSuccess, tableData]);


  function goBack() {
    navigate('/create-article');
  }

  const pollTaskProgress = (taskId) => {
    const interval = setInterval(() => {
      getTaskProgress(taskId)
        .then((res) => {
          const status = res.data.status;


          if (status === "success") {
            clearInterval(interval);
            setRequestModalActive(false);
            successAlertRef.current?.show("Successfully fetched published article.");
            setTimeout(() => successAlertRef.current?.close(), 5000);

            tableRef.current?.refetchData();
          } else if (status === "failure" || status === "FAILURE") {
            clearInterval(interval);
            setRequestModalActive(false);
            errorAlertRef.current?.show("Task failed. Please try again.");
            setTimeout(() => errorAlertRef.current?.close(), 5000);
          }
        })
        .catch((err) => {
          clearInterval(interval);
          setRequestModalActive(false);
          errorAlertRef.current?.show("Error fetching task progress. Please try again.");
          setTimeout(() => errorAlertRef.current?.close(), 5000);
        });
    }, 2000); // Poll every 2 seconds
  };


  const columnHelper = createColumnHelper<PublishedArticle>();

  // Define columns
  const columnDefs: ColumnDef<PublishedArticle, any>[] = [
    columnHelper.accessor((row) => row.title, {
      id: 'Article Title',
      header: "Article Title",
      cell: (props) => (
        <span>{props.row.original.title}</span>
      ),
      enableGlobalFilter: true,
      enableSorting: false,
    }),
    columnHelper.accessor((row) => row.published_date, {
      id: 'Published date',
      header: "Published date",
      cell: (props) => {
        const selectedDate = props.row.original.published_date
        if (!selectedDate) return <span data-nonclickable="true" style={{ cursor: "default" }}>---</span>;

        const getRelativeTime = (dateString: string) => {
          const createdDateObj = new Date(dateString);
          const now = new Date();
          const timeDiff = now.getTime() - createdDateObj.getTime();

          const seconds = Math.floor(Math.abs(timeDiff) / 1000);
          const minutes = Math.floor(seconds / 60);
          const hours = Math.floor(minutes / 60);
          const days = Math.floor(hours / 24);


          // PAST TIME
          if (seconds < 60) return "just now";
          if (minutes < 60) return minutes === 1 ? "a minute ago" : `${minutes} minutes ago`;
          if (hours < 24) return hours === 1 ? "an hour ago" : `${hours} hours ago`;
          if (days > 30) {
            const day = createdDateObj.getDate();
            const month = createdDateObj.toLocaleString('default', { month: 'short' });
            const year = createdDateObj.getFullYear().toString().slice(-2);
            return `${day} ${month}, ${year}`;
          }
          return days === 1 ? "a day ago" : `${days} days ago`;
        };

        return <span data-nonclickable="true" style={{ cursor: "default", color: "#000" }}>{getRelativeTime(selectedDate)}</span>;
      },

    }),
    columnHelper.accessor((row) => row.gsc_position, {
      id: 'GSC Position',
      header: "GSC Position",
      cell: (props) => {
        if (props.row.original.gsc_position) {
          return <span>{props.row.original.gsc_position}</span>
        } else {
          return <span>-</span>
        }
      },
      enableGlobalFilter: false,
    }),
    columnHelper.display({
      id: 'Action',
      header: "Action",
      cell: (props) => (
        <span><LinkButton linkTo={`/articles/edit/${props.row.original.article_uid}/`}
          text={"View"}
          type={"success"}
          width={"100px"}
          outlined={true}
          additionalClassList={["is-small", "more-rounded-borders"]} />
        </span>
      ),
    }),
  ];

  return (
    <div className="wp-pub-art-topic-card">
      {/* <span className={"back-btn"} onClick={goBack}>
        <svg className="back-btn" width="30" height="24" viewBox="0 0 30 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M26.0435 12.0003H2.82031M2.82031 12.0003L12.8382 1.98242M2.82031 12.0003L12.8382 22.0181" stroke="black" stroke-opacity="0.5" stroke-width="3" />
        </svg>
      </span> */}
      <h1 className={"is-size-4 has-text-centered"}>Optimize Your Published Blogs</h1>
      <p className={"is-size-6"}>Pull in your published posts and improve their Google rankings with a click.</p>
      <div className={`table-container`}>
        <AbunTable
          ref={tableRef}
          serverSide={true}
          apiUrl="/api/frontend/get-all-published-article/"
          id="article-titles-table"
          tableContentName={"Wordpress Published Articles"}
          // tableData={tableData}
          columnDefs={columnDefs}
          pageSizes={pageSizes}
          initialPageSize={pageSizes[6]}
          enableSorting={true}
          noDataText={"No Projects Found."}
          searchboxPlaceholderText={"Search keywords projects..."}
          transformResponse={(rawData) => ({
            data: rawData.articles,
            total: rawData.total,
          })}

        />
      </div>

      <AbunModal active={requestModalActive}
        headerText={""}
        closeable={false}
        hideModal={() => {
          setRequestModalActive(false)
        }}>
        <div className={"loadingData w-100 is-flex is-justify-content-center is-align-items-center"}>
          <AbunLoader show={requestModalActive} height="20vh" />
        </div>
        <p className={"is-size-4 has-text-centered mb-4"}>{modalText}</p>
      </AbunModal>

      <SuccessAlert ref={successAlertRef} />
      <ErrorAlert ref={errorAlertRef} />
    </div>
  )
}
// Export with admin check
export default withAdminAndProductionCheck(OptimizePublishedArticle);