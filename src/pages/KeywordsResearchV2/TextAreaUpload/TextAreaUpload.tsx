import { Autocomplete, Box, TextField } from '@mui/material';
import Chip from '@mui/material/Chip';
import { useMutation } from '@tanstack/react-query';
import React, { MutableRefObject, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import EarthFlag from '../../../assets/images/earth-flag.webp';
import AbunModal from '../../../components/AbunModal/AbunModal';
import GenericButton from '../../../components/GenericButton/GenericButton';
import { uploadKeywordsMutationV2 } from '../../../utils/api';
import countries from '../../../utils/constants/CountriesforSerp';
import { hasDuplicates } from '../../../utils/misc';

export interface CountryType {
  location_code: number;
  location_name: string;
  country_iso_code: string;
}

export interface TextAreaUploadProps {
  failAlertRef: MutableRefObject<any>;
  successAlertRef: MutableRefObject<any>;
  googleSuggestionsEnabled: boolean;
  onGetSuggestions?: (keyword: string) => Promise<string[]>;
  addKeywordsDone: () => void;
  addKeywordsFailed: (message: string) => void;
  countryCode: string;
  isEnableOnTrialPlan?: boolean;
  selectedPageName: string;
}

export default function TextAreaUpload(props: TextAreaUploadProps) {
  const [selectedLocation, setSelectedLocation] = useState<CountryType>({
    "location_code": 1,
    "location_name": "Global",
    "country_iso_code": "ZZ"
  });
  const [inputKeywords, setInputKeywords] = useState<string[]>([]);
  const [textAreaValue, setTextAreaValue] = useState("");
  const [isLoading] = useState(false);
  const [modalText, setModalText] = useState("");
  const [longInputKeywords, setLongInputKeywords] = useState(["", "", ""]);
  const [requestModalActive, setRequestModalActive] = useState(false);
  const keywordSuggestionsMap: Record<string, string[]> = {};
  const navigate = useNavigate();


  const { failAlertRef, successAlertRef, googleSuggestionsEnabled, onGetSuggestions } = props;

  // ------------------- EFFECTS -------------------
  const keywordsMutation = useMutation(uploadKeywordsMutationV2);

  useEffect(() => {
    // Find the country that matches the `country_iso_code` with `pageData.country_code`
    if (props.countryCode !== "ZZ") {
      const matchedCountry = countries.find(
        (country) => country.country_iso_code === props.countryCode.toUpperCase()
      );
      // If a match is found, update the selected location
      if (matchedCountry) {
        setSelectedLocation(matchedCountry);
      }
    }
  }, [props.countryCode]);


  function uploadManualKeywords() {
    // Validate keyword
    if (!inputKeywords || inputKeywords.length === 0) {
      return;
    }

    // if inputKeywords are valid, proceed with mutation
    keywordsMutation.mutate({
      keywords: inputKeywords,
      selectedLocation: selectedLocation,
      projectName: "Manual Keywords Upload - " + inputKeywords[0].substring(0, 15)
    }, {
      onSuccess: (data) => {
        let responseData = (data as any)["data"];
        if (responseData["status"] === "rejected") {
          if (responseData["reason"] === "max_limit_reached") {
            failAlertRef.current?.show("Keywords generation request failed. " +
              "You have reached your max Keywords generation limit for the month.");
          } else if (responseData["reason"] === "blocked_keyword_used") {
            failAlertRef.current?.show(responseData["message"]);
          } else if (responseData["reason"] === "no_keywords_found") {
            failAlertRef.current?.show("No keywords found. Please try with different keywords.");
          } else {
            failAlertRef.current?.show(
              `Keywords generation request failed. Error ID: ${responseData["reason"]}`
            );
          }
          setTimeout(() => {
            failAlertRef.current?.close();
          }, 7000);
        } else {
          props.addKeywordsDone();
          successAlertRef.current?.show("Keywords uploaded successfully.");
          setTimeout(() => {
            successAlertRef.current?.close();
          }, 3000);
        }
      },
      onError: () => {
        props.addKeywordsFailed("Keywords upload failed. Please try again!");
      }
    });
  }

  const addKeywordField = () => {
    setLongInputKeywords([...longInputKeywords, ""]);
  };

  const removeKeywordField = (index: number) => {
    setLongInputKeywords(longInputKeywords.filter((_, i) => i !== index));
  };

  const handleKeywordChange = (value: string, index: number) => {
    const newKeywords = [...longInputKeywords];
    newKeywords[index] = value;
    setLongInputKeywords(newKeywords);
  };

  async function uploadKeywords(projectName: string) {
    // Validate keyword
    if (!longInputKeywords || longInputKeywords.length === 0) {
      return;
    }
    setModalText("Processing request. Please wait...");
    setRequestModalActive(true);
    const filteredKeywords = longInputKeywords.filter(keyword => keyword !== "");
    if (hasDuplicates(filteredKeywords)) {
      failAlertRef.current.show("Please enter unique keywords.");
      setTimeout(() => {
        failAlertRef.current?.close();
      }, 5000);
      return;
    }

    if (onGetSuggestions) {
      await Promise.all(filteredKeywords.map(async (keyword) => {
        try {
          const suggestions = await onGetSuggestions(keyword);
          if (suggestions && suggestions.length > 0) {
            keywordSuggestionsMap[keyword] = suggestions;
          }
        } catch (error) {
          console.error(`Failed to fetch suggestions for keyword: ${keyword}`, error);
        }
      }));
    }

    // Prepare a combined array of all keywords and their suggestions
    const allKeywordSuggestions: string[] = [];
    Object.entries(keywordSuggestionsMap).forEach(([keyword, suggestions]) => {
      allKeywordSuggestions.push(keyword, ...suggestions);
    });

    // Combine the first 5 letters of each keyword into a single project name
    const derivedProjectName = longInputKeywords
      .map(keyword => keyword.slice(0, 5)) // Take the first 5 letters of each keyword
      .join(" "); // Join them with a space

    keywordsMutation.mutate({
      keywords: allKeywordSuggestions,
      selectedLocation: selectedLocation,
      projectName: `${projectName} - ${derivedProjectName}`,
      keywordsAddedUsing: props.isEnableOnTrialPlan ? "input-box" : "csv"
    }, {
      onSuccess: (data) => {
        setRequestModalActive(false);
        let responseData = (data as any)["data"];
        if (responseData["status"] === "rejected") {
          if (responseData["reason"] === "max_limit_reached") {
            failAlertRef.current?.show("Keywords generation request failed. " +
              "You have reached your max Keywords generation limit for the month.");
          } else if (responseData["reason"] === "blocked_keyword_used") {
            failAlertRef.current?.show(responseData["message"]);
          } else if (responseData["reason"] === "no_keywords_found") {
            failAlertRef.current?.show("No keywords found. Please try with different keywords.");
          } else {
            failAlertRef.current?.show(
              `Keywords generation request failed. Error ID: ${responseData["reason"]}`
            );
          }
          setTimeout(() => {
            failAlertRef.current?.close();
          }, 7000);
        } else {
          // Extract the project_id from the response data
          const projectId = responseData["project_id"];

          navigate({
            pathname: `/keyword-research`,
            search: `?keywordProjectId=${projectId}`
          });
          successAlertRef.current?.show("Keywords uploaded successfully.");
          setTimeout(() => {
            successAlertRef.current?.close();
          }, 3000);
        }

      },
      onError: (error) => {
        setRequestModalActive(false);
        if ((error as any).err_id === "max_limit_reached") {
          // Specific handling for max limit reached error
          failAlertRef.current?.show("Keywords generation request failed. Max limit reached!");
        } else {
          // General error handling
          props.addKeywordsFailed("Keywords upload failed. Please try again!");
        }
      }
    });
  }

  const handleManualKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Comma" || e.key === "," || e.key === "Enter") {
      e.preventDefault();
      let keywords = textAreaValue?.trim().split('\n').map(line => line.split(',').map(value => value.trim()).filter(value => value !== '' && !inputKeywords.includes(value))).flat();
      if (keywords) {
        setInputKeywords([...inputKeywords, ...keywords]);
        setTextAreaValue("");
      }
    }
  };

  const handleDelete = (keywordToDelete: string) => () => {
		setInputKeywords((prevKeywords) => prevKeywords.filter((keyword) => keyword !== keywordToDelete));		
	};

	const handleEdit = (keywordToEdit: string) => () => {
		setTextAreaValue(keywordToEdit);
		setInputKeywords((prevKeywords) => prevKeywords.filter((keyword) => keyword !== keywordToEdit));
	};



  return (
    <div className={`ai-keyword-research-content ${!googleSuggestionsEnabled ? 'no-margin' : ''}`}>
			{ (googleSuggestionsEnabled) && (
			<>
			<h1>
				Find High-Intent Keywords in Seconds
			</h1>
			<p className={"is-size-6"}>
				Type in a seed keyword to get longtail ideas that can help<br/> your site rank higher
			</p>
			</>
			)}
      <form className={"ai-keyword-research-form"}>
        <div className={googleSuggestionsEnabled ? "form-group location-select" : "location-input"}>
          <Autocomplete
            id="manual-keywords-location-select-autocomplete"
            sx={{ width: 200 }}
            options={countries}
            value={selectedLocation}
            autoHighlight
            getOptionLabel={(option) => option.country_iso_code !== "ZZ" ? `${option.location_name} (${option.country_iso_code})` : option.location_name}
            isOptionEqualToValue={(option, value) => option.location_code === value.location_code}
            renderOption={(props, option) => (
              <Box component="li" sx={{ '& > img': { mr: 2, flexShrink: 0 } }} {...props}>
                <img
                  loading="lazy"
                  width="20"
                  srcSet={option.country_iso_code !== "ZZ" ? `https://flagcdn.com/w40/${option.country_iso_code.toLowerCase()}.png 2x` : EarthFlag}
                  src={option.country_iso_code !== "ZZ" ? `https://flagcdn.com/w20/${option.country_iso_code.toLowerCase()}.png` : EarthFlag}
                  alt=""
                />
                {option.location_name} ({option.country_iso_code})
              </Box>
            )}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Location"
                inputProps={{
                  ...params.inputProps,
                  autoComplete: 'off',
                }}
              />
            )}
            onChange={(_event, option) => {
              if (option) {
                setSelectedLocation(option);
              }
            }}
          />
        </div>

        {googleSuggestionsEnabled ? (
          <>
            {longInputKeywords.map((keyword, index) => (
              <div key={index} className={`form-group ${index >= 1 ? "extras" : ""}`}>
                {index >= 1 && <div className={"balancer remove-extra-keyword"} />}
                <TextField id="outlined-basic" label={inputKeywords.length === 1 ? "Keyword" : `Keyword ${index + 1}`} variant="outlined" sx={{ width: 375 }} value={keyword} onChange={(e) => handleKeywordChange(e.target.value, index)} />
                {index >= 1 && (
                  <button
                    type="button"
                    className="remove-extra-keyword"
                    onClick={() => removeKeywordField(index)}
                  >&times;</button>
                )}
              </div>
            ))}
            <div className="form-group add-more">
              <button
                type="button"
                className={`add-more-keywords ${keywordsMutation.isLoading || !longInputKeywords[0] || longInputKeywords.length >= 50
                  ? "disabled" : ""}`}
                onClick={addKeywordField}
              >
                + Add More
              </button>
            </div>
          </>
        ) : (
          <textarea
            className="input"
            id="outlined-basic"
            value={textAreaValue}
            onChange={(e) => setTextAreaValue(e.target.value)}
            onKeyDown={handleManualKeyDown}
            placeholder="Enter Keywords separated by commas (,)"
            style={{ height: "8rem", width: "29rem" }}
          />
        )}

        {googleSuggestionsEnabled ? (
          <GenericButton text={requestModalActive ? "Uploading. Please Wait..." :
            "PROCEED"}
            // icon={"cloud-upload"}
            type={"success"}
            disable={keywordsMutation.isLoading || longInputKeywords.length === 0}
            additionalClassList={["mt-5"]}
            clickHandler={() => uploadKeywords(props.selectedPageName)}
          />
        ) : (
          <GenericButton text={keywordsMutation.isLoading ? "Uploading. Please Wait..." : `Upload ${inputKeywords.length} Selected Keywords`}
            icon={"cloud-upload"}
            type={"success"}
            disable={keywordsMutation.isLoading}
            additionalClassList={["mt-5"]}
            clickHandler={uploadManualKeywords} />
        )}
        {inputKeywords.length > 0 && (
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, marginTop: '2rem', padding: '.5rem' }}>
            {inputKeywords.map((keyword, index) => (
              <Chip
                key={index}
                label={keyword}
                variant="outlined"
                onDelete={handleDelete(keyword)}
                onClick={handleEdit(keyword)}
              />
            ))}
          </Box>
        )}
      </form>

      {/* ------------------------------ ONGOING REQUEST MODAL ------------------------------ */}
      <AbunModal active={requestModalActive}
        headerText={""}
        modelWidth="450px"
        closeable={false}
        hideModal={() => {
          setRequestModalActive(false);
        }}
        backgroundClass="lg-tail-kw-modal-background"
        modalCardClass="lg-tail-kw-abun-modal-card"
      >
        <div className="loader-container" aria-live="polite" aria-busy={isLoading}
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            marginTop: '30px'
          }}>
          {/* ldrs loader */}
          <l-quantum size="60" speed="1.75" color="#2E64FE"></l-quantum>
        </div>
        <h3 className="modal-header-text">Please hold on</h3>
        <p className="modal-subtext">{modalText}</p>
      </AbunModal>
    </div>
  );
}
