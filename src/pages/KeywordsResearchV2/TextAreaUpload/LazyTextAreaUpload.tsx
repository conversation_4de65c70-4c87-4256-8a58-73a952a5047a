import React, { lazy, Suspense } from 'react';
import AbunLoader from '../../../components/AbunLoader/AbunLoader';
import { MutableRefObject } from 'react';

// Lazy load the TextAreaUpload component
const TextAreaUploadLazy = lazy(() => import('./TextAreaUpload'));

// Props interface matching the original component
export interface TextAreaUploadProps {
  failAlertRef: MutableRefObject<any>;
  successAlertRef: MutableRefObject<any>;
  googleSuggestionsEnabled: boolean;
  onGetSuggestions?: (keyword: string) => Promise<string[]>;
  addKeywordsDone: () => void;
  addKeywordsFailed: (message: string) => void;
  countryCode: string;
  isEnableOnTrialPlan?: boolean;
  selectedPageName: string;
}

// Wrapper component that handles the lazy loading with a fallback
export default function LazyTextAreaUpload(props: TextAreaUploadProps) {
  return (
    <Suspense fallback={
      <div className="ai-keyword-research-content">
        <div className="loadingData w-100 is-flex is-justify-content-center is-align-items-center">
          <AbunLoader show={true} height="20vh" />
        </div>
      </div>
    }>
      <TextAreaUploadLazy {...props} />
    </Suspense>
  );
}
