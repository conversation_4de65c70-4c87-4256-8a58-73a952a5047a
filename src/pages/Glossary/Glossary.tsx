import { useMutation } from '@tanstack/react-query';
import React, { useRef, useState } from 'react';
import { useLoaderData, useNavigate } from 'react-router-dom';
import { Tooltip } from 'react-tooltip';
import AbunLoader from "../../components/AbunLoader/AbunLoader";
import AbunModal from '../../components/AbunModal/AbunModal';
import ErrorAlert from '../../components/ErrorAlert/ErrorAlert';
import GenericButton from '../../components/GenericButton/GenericButton';
import SuccessAlert from '../../components/SuccessAlert/SuccessAlert';
import { withAdminAndProductionCheck } from '../../utils/adminAndProductionCheck';
import { glossaryMutation } from '../../utils/api';
import { pageURL } from '../routes';
import './Glossary.min.css';

interface UserData {
    verified: boolean
    username: string
    website: string
    email: string
    tz: string
    send_notif_email: boolean
}

const Glossary: React.FC = () => {
    // ----------------------------- LOADER -----------------------------
    const pageData: UserData = useLoaderData() as UserData;

    const [word, setWord] = useState('');
    const [loadingWord, setLoadingWord] = useState(false);
    const [proceedEnabled, setProceedEnabled] = useState(false);
    const [requestModalActive, setRequestModalActive] = useState(false);
    const navigate = useNavigate();

    const glossaryMut = useMutation(glossaryMutation);

    // --------------------------- Refs ---------------------------
    const successAlertRef = useRef<any>(null);
    const errorAlertRef = useRef<any>(null);

    const handleGenerateGlossary = () => {
        if (!word.trim()) return;
        // Trigger the mutation
        glossaryMut.mutate(
            { word },
            {
                onSuccess: (response) => {
                    const { project_name, task_id, project_id } = response.data;
                    localStorage.setItem('glossary_task_id', task_id);
                    successAlertRef.current?.show('Query submitted. It will take few minutes to complete.');
                    navigate(`/glossary-generator/${project_id}`);
                },
                onError: () => {
                    errorAlertRef.current?.show(
                        'Oops! Something went wrong. Please try again or contact support if the issue persists.'
                    );
                    setTimeout(() => {
                        errorAlertRef.current?.close();
                    }, 5000);
                },
                onSettled: () => {
                    setLoadingWord(false);
                },
            }
        );
    };


    function goBack() {
        navigate(pageURL['glossaryHome']);
    }

    // Track input changes and update word state
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newWord = e.target.value;
        setWord(newWord);

        // Enable the Proceed button when there is input
        setProceedEnabled(newWord.trim().length > 0 && pageData.verified);
    };

    return (
        <div className="glossary-card">
            <span className={"back-btn"} onClick={goBack}>
                <svg className="back-btn" width="30" height="24" viewBox="0 0 30 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M26.0435 12.0003H2.82031M2.82031 12.0003L12.8382 1.98242M2.82031 12.0003L12.8382 22.0181" stroke="black" stroke-opacity="0.5" stroke-width="3" />
                </svg>
            </span>
            <h1 className={"is-size-3 glossary-title"}>Glossary Article Creator</h1>
            <div className="glossary-content">
                <h1 className="glossary-subtitle">Enter a Topic for Create a Glossary Terms</h1>
                <p className="glossary-description">
                    Example, "Graphic Design" generates a list of niche-relevant terms.<br />
                    Approve the terms, then create & post content.
                </p>
                <div className="glossary-input-container">
                    <input
                        type="text"
                        placeholder="Topic"
                        className="glossary-input"
                        value={word}
                        onChange={handleInputChange}
                        onKeyDown={(e) => {
                            if (e.key === 'Enter' && proceedEnabled) {
                                handleGenerateGlossary();
                            }
                        }}
                    />

                </div>
                <div data-tooltip-id="proceedButtonTip" data-tooltip-content="Verify email to create glossary terms">
                    <GenericButton
                        text={"PROCEED"}
                        type="success"
                        additionalClassList={["mt-5"]}
                        clickHandler={handleGenerateGlossary}
                        disable={!proceedEnabled}
                    />
                    {!pageData.verified && (
                        <Tooltip id="proceedButtonTip" place="bottom" />
                    )}
                </div>
            </div>

            {/* ------------------------------ ONGOING REQUEST MODAL ------------------------------ */}
            <AbunModal
                active={requestModalActive}
                headerText={""}
                closeable={false}
                hideModal={() => setRequestModalActive(false)}
            >
                <div className="loadingData w-100 is-flex is-justify-content-center is-align-items-center">
                    <AbunLoader show={requestModalActive} height="20vh" />
                </div>
                <p className="is-size-4 has-text-centered mb-4">Generating Glossary Terms</p>
            </AbunModal>


            <SuccessAlert ref={successAlertRef} style={{ left: '60%' }} />
            <ErrorAlert ref={errorAlertRef} style={{ left: '60%' }} />

        </div>
    );
};

export default withAdminAndProductionCheck(Glossary);
