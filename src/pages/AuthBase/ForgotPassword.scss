@import "../../assets/themes/mainTheme";
@import "../../assets/fonts/customFonts";

// bulma overrides
$card-content-padding: 4rem;
$input-shadow: unset;
$input-border-color: $grey-dark;
$input-color: $grey-darker;
$input-placeholder-color: $grey-darker;

@import "bulma/sass/utilities/all";
@import "bulma/sass/helpers/all";
@import "bulma/sass/elements/button";
@import "bulma/sass/components/card";
@import "bulma/sass/form/all";


.forgot-container {
    padding-top: 120px;
    width: 650px!important;

    .heading-text{
        font-family: $epilogue-font !important;
        font-size: 3rem;
        font-weight: 600;
        line-height: 1.2em;
        text-align: center;
    }

    .subtitle-text{
        font-size: 1.5rem;
        text-align: center;
        font-family: $primary-font !important;
    }

    .login-card{
        border-radius: 16px;
        box-shadow: 0 8px 17px rgba(0,0,0,.161);
        width: 100%;

        .login-btn-border{
            background-color: #fff !important;
            border: 1px solid #000;
            border-radius: 9px!important;
            color: #000!important;
            font-family: $secondary-font;
            font-size: 18px;
            font-weight: 500;
            padding: 15px 50px 15px 60px;
            text-transform: uppercase;
            transition: .2s;


            &:hover{
                background: #fac44b !important;
                box-shadow: .25rem .25rem #000;
                transform: translate(-.25rem,-.25rem);
            }
        }

        .confirm-msg{
            font-family: $primary-font;
        }
    }

    .divider{
        margin-left: 10px;
        margin-right: 10px;
        color: #000;
  
        @media (max-width:600px) {
            display: none; 
        }
    }

    @media screen and (max-width:600px) {
        .user-forgot-password {
            display: flex;
            flex-direction: column;
        } 
    }

    .bricolage{
        font-family: $primary-font;
    }
}
